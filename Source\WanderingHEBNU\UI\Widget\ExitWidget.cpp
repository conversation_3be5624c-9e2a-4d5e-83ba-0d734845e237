#include "ExitWidget.h"

#include "Kismet/KismetSystemLibrary.h"

void UExitWidget::NativeConstruct()
{
	Super::NativeConstruct();

	if (ConfirmButton)
	{
		ConfirmButton->OnClicked.AddDynamic(this, &ThisClass::OnExitClicked);
	}
}

void UExitWidget::OnExitClicked()
{
	UKismetSystemLibrary::QuitGame(this, nullptr, EQuitPreference::Quit, true); 
}

// void UExitWidget::OnCancelClicked()
// {
// 	this->Close();
// 	this->SetInputGame();
//
// 	if (GetWorld())
// 	{
// 		APlayerController* PlayerController = GetWorld()->GetFirstPlayerController();
// 		if (PlayerController)
// 		{
// 			ACharacter* MainCharacter = Cast<ACharacter>(PlayerController->GetCharacter());
// 			MainCharacter -> GetCharacterMovement() -> SetMovementMode(MOVE_Walking);
// 		}
// 	}
// }