#include "Log.h"

void ULog::Info(const float Value, const bool bNotDisplay)
{
	const FString Message = FString::Printf(TEXT("时间：%s---%f"), *TimeNow(), Value);
	UE_LOG(LogTemp, Warning, TEXT("%s"), *Message);
	if (!bNotDisplay && GEngine)
	{
		GEngine -> AddOnScreenDebugMessage(-1, 5, FColor::Green, Message);
	}
}

void ULog::Info(const FString& Value, const bool bNotDisplay)
{
	const FString Message = FString::Printf(TEXT("时间：%s---%s"), *TimeNow(), *Value);
	UE_LOG(LogTemp, Warning, TEXT("%s"), *Message);
	if (!bNotDisplay && GEngine)
	{
		GEngine -> AddOnScreenDebugMessage(-1, 5, FColor::Green, Message);
	}
}

void ULog::Info(const char* Value, const bool bNotDisplay)
{
	const FString UTF8Value = FString(UTF8_TO_TCHAR(Value));
	const FString Message = FString::Printf(TEXT("时间：%s---%s"), *TimeNow(), *UTF8Value);
	UE_LOG(LogTemp, Warning, TEXT("%s"), *Message);
	if (!bNotDisplay && GEngine)
	{
		GEngine -> AddOnScreenDebugMessage(-1, 5, FColor::Green, Message);
	}
}

FString ULog::TimeNow()
{
	const FDateTime Time = FDateTime::Now();
	const int Year = Time . GetYear();
	const int Month = Time . GetMonth();
	const int Day = Time . GetDay();
	const int Hour = Time . GetHour();
	const int Minute = Time . GetMinute();
	const int Second = Time . GetSecond();
	return FString::Printf(TEXT("%d-%d-%d %d:%d:%d"), Year, Month, Day, Hour, Minute, Second);
}
