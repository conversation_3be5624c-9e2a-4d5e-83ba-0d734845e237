#include "Water.h"

#include "Components/AudioComponent.h"

AWater::AWater()
{
	PrimaryActorTick . bCanEverTick = true;

	SurfaceComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("SurfaceComponent"));
	SurfaceComponent -> SetCollisionProfileName("NoCollision");
	SurfaceComponent -> SetCastShadow(false);
	SetRootComponent(SurfaceComponent);

	VolumeComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("VolumeComponent"));
	VolumeComponent -> SetCollisionProfileName("NoCollision");
	VolumeComponent -> SetCastShadow(false);
	VolumeComponent -> SetHiddenInGame(true, true);
	VolumeComponent -> SetupAttachment(GetRootComponent());

	RangeComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("RangeComponent"));
	RangeComponent -> SetCollisionProfileName("NoCollision");
	RangeComponent -> SetBoxExtent(FVector(50));
	RangeComponent -> SetupAttachment(GetRootComponent());

	PostProcessComponent = CreateDefaultSubobject<UPostProcessComponent>(TEXT("PostProcessComponent"));
	PostProcessComponent -> BlendRadius = 0;
	PostProcessComponent -> bUnbound = false;
	PostProcessComponent -> bUseAttachParentBound = true;
	PostProcessComponent -> SetupAttachment(RangeComponent);

	AudioComponent -> SetupAttachment(GetRootComponent());
}

void AWater::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);
	UpdateWater();
}

#if WITH_EDITOR

void AWater::PostEditChangeProperty(struct FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);
	UpdateWater();
}

#endif

void AWater::BeginPlay()
{
	Super::BeginPlay();
}

void AWater::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AWater::UpdateWater()
{
	const FVector2D Scale3D = Extent / 100;
	SurfaceComponent -> SetRelativeScale3D(FVector(Scale3D, 1));
	VolumeComponent -> SetRelativeScale3D(FVector(1, 1, Depth / 100));
	VolumeComponent -> SetRelativeLocation(FVector(0, 0, -Depth / 2));

	RangeComponent -> SetRelativeScale3D(FVector(1, 1, Depth / 100));
	RangeComponent -> SetRelativeLocation(FVector(0, 0, -Depth / 2));

	if (WaterMaterial)
	{
		SurfaceComponent -> SetMaterial(0, WaterMaterial);
		// 水下部分开发摆放用，实际游戏水下使用的是后处理
		VolumeComponent -> SetMaterial(0, WaterMaterial);
	}
	if (VolumeMaterial)
	{
		PostProcessComponent -> Settings . AddBlendable(VolumeMaterial, 1);
		PostProcessComponent -> bEnabled = true;
	}
}
