#pragma once

#include "CoreMinimal.h"
#include "Engine/Texture2D.h"
#include "ImageUtils.generated.h"

/**
 * 图片处理工具类
 * 提供图片编码、解码、压缩等功能
 */
UCLASS(BlueprintType)
class WANDERINGHEBNU_API UImageUtils : public UObject
{
	GENERATED_BODY()

public:
	/**
	 * 将图片文件转换为Base64字符串
	 * @param FilePath 图片文件路径
	 * @param OutBase64 输出的Base64字符串
	 * @param OutFormat 输出的图片格式
	 * @param OutSize 输出的文件大小
	 * @return 是否成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static bool ImageFileToBase64(const FString& FilePath, FString& OutBase64, FString& OutFormat, int64& OutSize);

	/**
	 * 将Base64字符串转换为UTexture2D
	 * @param Base64String Base64编码的图片数据
	 * @param Format 图片格式
	 * @return 生成的纹理对象
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static UTexture2D* Base64ToTexture2D(const FString& Base64String, const FString& Format);

	/**
	 * 压缩图片数据
	 * @param OriginalData 原始图片数据
	 * @param Quality 压缩质量 (0-100)
	 * @param OutCompressedData 压缩后的数据
	 * @return 是否成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static bool CompressImageData(const TArray<uint8>& OriginalData, int32 Quality, TArray<uint8>& OutCompressedData);

	/**
	 * 验证图片格式是否支持
	 * @param Format 图片格式
	 * @return 是否支持
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static bool IsValidImageFormat(const FString& Format);

	/**
	 * 验证图片大小是否在限制内
	 * @param Size 图片大小（字节）
	 * @return 是否在限制内
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static bool IsValidImageSize(int64 Size);

	/**
	 * 从文件路径获取图片格式
	 * @param FilePath 文件路径
	 * @return 图片格式
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static FString GetImageFormatFromPath(const FString& FilePath);

	/**
	 * 获取支持的图片格式列表
	 * @return 支持的格式数组
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Utils")
	static TArray<FString> GetSupportedImageFormats();

private:
	// 最大图片大小限制（2MB）
	static constexpr int64 MAX_IMAGE_SIZE = 2 * 1024 * 1024;
	
	// 支持的图片格式
	static const TArray<FString> SupportedFormats;
};
