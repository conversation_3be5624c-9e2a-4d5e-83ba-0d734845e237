#include "TCPSocket.h"

#include "Common/TcpListener.h"
#include "../../../Common/Log.h"

void UTCPSocket::Start(const FTCPLink& Link)
{
	Setting = Link;
	const TWeakObjectPtr<UTCPSocket> WeakObjPtr = TWeakObjectPtr<UTCPSocket>(this);
	Worker = MakeShareable(new FTCPWorker(Setting, WeakObjPtr));
	Worker -> Start();
}

void UTCPSocket::Close()
{
	if (Worker) { Worker -> Stop(); }
	Worker . Reset();
}

bool UTCPSocket::SendStream(NetPacket::FStream Stream) const
{
	return Send(Stream . ToString());
}

bool UTCPSocket::Send(const FString& Message) const
{
	if (!Worker || !Worker -> IsConnected()) { return false; }
	TArray<uint8> Bytes;
	const FTCHARToUTF8 Converter(*Message);
	Bytes . Append(reinterpret_cast<const uint8*>(Converter . Get()), Converter . Length());
	Worker -> AddMessage(Bytes);
	return true;
}

void UTCPSocket::OnConnected(const TWeakObjectPtr<UTCPSocket> Self)
{
	if (!Self . IsValid()) { return; }
	if (OnSocketStart) { OnSocketStart(Setting . ServerIP, Setting . ServerPort); }
}

void UTCPSocket::OnDisconnected(const TWeakObjectPtr<UTCPSocket> Self)
{
	if (!Self . IsValid()) { return; }
	if (OnSocketClose) { OnSocketClose(); }
}

void UTCPSocket::OnMessageReceived(const TWeakObjectPtr<UTCPSocket> Self)
{
	if (!Self . IsValid()) { return; }
	if (!Worker) { return; }
	TArray<uint8> Raw = Worker -> ReadMessage();
	Raw . Add(0);
	const FString Dirty = FString(UTF8_TO_TCHAR(reinterpret_cast<const char*>(Raw . GetData())));
	const FString SenderIP = Setting . ServerIP;
	int SenderPort = Setting . ServerPort;
	TArray<FString> Messages;
	Dirty . ParseIntoArray(Messages, TEXT("\n"), true);
	for (FString Message : Messages)
	{
		if (OnReceivedMessage) { OnReceivedMessage(Message, SenderIP, SenderPort); }
		if (OnReceivedMessageInGameThread)
		{
			AsyncTask(ENamedThreads::GameThread, [this, Message, SenderIP, SenderPort]()
			{
				if (OnReceivedMessageInGameThread) { OnReceivedMessageInGameThread(Message, SenderIP, SenderPort); }
			});
		}
	}
}

FTCPWorker::FTCPWorker(const FTCPLink& Link, const TWeakObjectPtr<UTCPSocket> InOwner)
	: Setting(Link),
	  ThreadSpawner(InOwner)
{
}

FTCPWorker::~FTCPWorker()
{
	Stop();
	if (Thread)
	{
		Thread -> WaitForCompletion();
		delete Thread;
		Thread = nullptr;
	}
}

void FTCPWorker::Start()
{
	const FString ThreadName = TEXT("TCPWorker");
	Thread = FRunnableThread::Create(this, *ThreadName, 128 * 1024, TPri_Normal);
}

TArray<uint8> FTCPWorker::ReadMessage()
{
	TArray<uint8> Message;
	InBox . Dequeue(Message);
	return Message;
}

bool FTCPWorker::Init()
{
	bRun = true;
	bConnected = false;
	return true;
}

uint32 FTCPWorker::Run()
{
	ISocketSubsystem* SocketSubsystem = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM);
	while (bRun)
	{
		FDateTime TimeBeginningOfTick = FDateTime::UtcNow();
		if (!bConnected)
		{
			Socket = SocketSubsystem -> CreateSocket(NAME_Stream, TEXT("Default"), false);
			if (!Socket) { return 0; }
			Socket -> SetReceiveBufferSize(Setting . BufferSize, Setting . BufferSize);
			Socket -> SetSendBufferSize(Setting . BufferSize, Setting . BufferSize);
			FIPv4Address IP;
			FIPv4Address::Parse(Setting . ServerIP, IP);
			TSharedRef<FInternetAddr> Address = SocketSubsystem -> CreateInternetAddr();
			Address -> SetIp(IP . Value);
			Address -> SetPort(Setting . ServerPort);
			bConnected = Socket -> Connect(*Address);
			if (bConnected)
			{
				AsyncTask(ENamedThreads::GameThread, [this]()
				{
					ThreadSpawner . Get() -> OnConnected(ThreadSpawner);
				});
			}
			else
			{
				AsyncTask(ENamedThreads::GameThread, []()
				{
					ULog::Info(TEXT("TCP: 连接失败"), true);
				});
				bRun = false;
			}
			continue;
		}
		if (!Socket)
		{
			AsyncTask(ENamedThreads::GameThread, []()
			{
				ULog::Info(TEXT("TCP: Socket创建失败"), true);
			});
			bRun = false;
			continue;
		}
		Socket -> SetNonBlocking(true);
		int BytesRead;
		uint8 Dummy;
		if (!Socket -> Recv(&Dummy, 1, BytesRead, ESocketReceiveFlags::Peek))
		{
			bRun = false;
			continue;
		}
		Socket -> SetNonBlocking(false);
		while (!OutBox . IsEmpty())
		{
			TArray<uint8> Bytes;
			OutBox . Dequeue(Bytes);
			if (!SendBytes(Bytes))
			{
				bRun = false;
				ULog::Info(TEXT("TCP: 数据发送失败"), true);
			}
		}
		uint32 PendingDataSize = 0;
		TArray<uint8> ReceivedData;
		int32 BytesReadTotal = 0;
		while (bRun)
		{
			if (!Socket -> HasPendingData(PendingDataSize)) { break; }
			ReceivedData . SetNumUninitialized(BytesReadTotal + PendingDataSize);
			BytesRead = 0;
			if (!Socket -> Recv(ReceivedData . GetData() + BytesReadTotal, PendingDataSize, BytesRead))
			{
				AsyncTask(ENamedThreads::GameThread, []()
				{
					ULog::Info(TEXT("TCP: 消息读取失败"), true);
				});
				break;
			}
			BytesReadTotal += BytesRead;
		}
		if (bRun && ReceivedData . Num() != 0)
		{
			InBox . Enqueue(ReceivedData);
			AsyncTask(ENamedThreads::GameThread, [this]()
			{
				ThreadSpawner . Get() -> OnMessageReceived(ThreadSpawner);
			});
		}
		FDateTime TimeEndOfTick = FDateTime::UtcNow();
		FTimespan TickDuration = TimeEndOfTick - TimeBeginningOfTick;
		const float SecondsThisTickTook = TickDuration . GetTotalSeconds();
		const float TimeToSleep = Setting . TickGap - SecondsThisTickTook;
		if (TimeToSleep > 0) { FPlatformProcess::Sleep(TimeToSleep); }
	}
	bConnected = false;
	AsyncTask(ENamedThreads::GameThread, [this]()
	{
		ThreadSpawner . Get() -> OnDisconnected(ThreadSpawner);
	});
	SocketShutdown();
	if (Socket)
	{
		delete Socket;
		Socket = nullptr;
	}
	return 0;
}

void FTCPWorker::SocketShutdown()
{
	if (Socket) { Socket -> Close(); }
}

bool FTCPWorker::SendBytes(const TArray<uint8>& Bytes)
{
	if (!Socket || Socket -> GetConnectionState() != SCS_Connected) { return false; }
	int32 BytesSent = 0;
	return Socket -> Send(Bytes . GetData(), Bytes . Num(), BytesSent);
}
