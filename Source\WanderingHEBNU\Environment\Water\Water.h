#pragma once

#include "CoreMinimal.h"
#include "Components/BoxComponent.h"
#include "Components/PostProcessComponent.h"
#include "../../Base/BaseActor.h"
#include "Water.generated.h"

UCLASS()
class AWater : public ABaseActor
{
	GENERATED_BODY()

public:
	AWater();

	virtual void OnConstruction(const FTransform& Transform) override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(struct FPropertyChangedEvent& PropertyChangedEvent) override;
#endif

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水设置", DisplayName = "水深")
	float Depth = 500;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水设置", DisplayName = "水大小")
	FVector2D Extent = FVector2D(500, 1000);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水设置", DisplayName = "水材质")
	TObjectPtr<UMaterialInterface> WaterMaterial;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水设置", DisplayName = "水下材质(后处理)")
	TObjectPtr<UMaterialInterface> VolumeMaterial;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水组件", DisplayName = "水表面模型组件")
	TObjectPtr<UStaticMeshComponent> SurfaceComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水组件", DisplayName = "水体积模型组件")
	TObjectPtr<UStaticMeshComponent> VolumeComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水组件", DisplayName = "水体积范围碰撞组件")
	TObjectPtr<UBoxComponent> RangeComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "水组件", DisplayName = "水下后处理组件")
	TObjectPtr<UPostProcessComponent> PostProcessComponent;

	void UpdateWater();
};
