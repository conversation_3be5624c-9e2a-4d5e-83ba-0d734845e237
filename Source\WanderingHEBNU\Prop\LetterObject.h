#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "LetterObject.generated.h"

UCLASS()
class ALetterObject : public AActor
{
	GENERATED_BODY()

public:
	ALetterObject();

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterContent(const FString& Content);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	FString GetLetterContent();

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterStyleIndex(int Index);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterLocation(const FVector& Location);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterRotation(const FRotator& Rotation);

	// 图片相关方法
	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterImageData(const FString& ImageData);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	FString GetLetterImageData() const;

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterImageFormat(const FString& ImageFormat);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	FString GetLetterImageFormat() const;

	UFUNCTION(BlueprintCallable, Category="信件设置")
	bool HasLetterImage() const;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "信件设置", DisplayName = "静态网格体组件")
	TObjectPtr<UStaticMeshComponent> Mesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件内容")
	FString LetterContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件样式序号")
	int LetterStyleIndex;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件Location")
	FVector LetterLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件Rotation")
	FRotator LetterRotation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件创建时间")
	FDateTime CreateTime;

	// 图片相关属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件图片数据")
	FString LetterImageData;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件图片格式")
	FString LetterImageFormat;
};
