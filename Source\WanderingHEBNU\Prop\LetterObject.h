#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "LetterObject.generated.h"

UCLASS()
class ALetterObject : public AActor
{
	GENERATED_BODY()

public:
	ALetterObject();

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterContent(const FString& Content);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	FString GetLetterContent();

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterStyleIndex(int Index);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterLocation(const FVector& Location);

	UFUNCTION(BlueprintCallable, Category="信件设置")
	void SetLetterRotation(const FRotator& Rotation);

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "信件设置", DisplayName = "静态网格体组件")
	TObjectPtr<UStaticMeshComponent> Mesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件内容")
	FString LetterContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件样式序号")
	int LetterStyleIndex;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件Location")
	FVector LetterLocation;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件Rotation")
	FRotator LetterRotation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="信件设置", DisplayName="信件创建时间")
	FDateTime CreateTime;
};
