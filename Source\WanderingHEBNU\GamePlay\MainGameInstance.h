#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "MainGameInstance.generated.h"

class ANetManager;

UCLASS()
class UMainGameInstance : public UGameInstance
{
	GENERATED_BODY()

public:
	virtual void Init() override;

	virtual bool Tick(float DeltaSeconds);

	void SetNetManager(const TObjectPtr<ANetManager>& Value) { NetManager = Value; }

	TObjectPtr<ANetManager> GetNetManager() const { return NetManager; }

protected:
	FTickerDelegate TickDelegate;

	FTSTicker::FDelegateHandle TickDelegateHandle;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "网络管理")
	TObjectPtr<ANetManager> NetManager = nullptr;
};
