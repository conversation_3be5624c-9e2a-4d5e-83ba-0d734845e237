#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "BaseActor.generated.h"

UCLASS()
class ABaseActor : public AActor
{
	GENERATED_BODY()

public:
	ABaseActor();

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "基础设置", DisplayName = "音频组件")
	TObjectPtr<UAudioComponent> AudioComponent;
};
