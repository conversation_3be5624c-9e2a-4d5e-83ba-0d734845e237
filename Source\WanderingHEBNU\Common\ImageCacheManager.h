#pragma once

#include "CoreMinimal.h"
#include "Engine/Texture2D.h"
#include "ImageCacheManager.generated.h"

/**
 * 图片缓存管理器
 * 用于缓存已加载的图片纹理，避免重复解码
 */
UCLASS(BlueprintType)
class WANDERINGHEBNU_API UImageCacheManager : public UObject
{
	GENERATED_BODY()

public:
	/**
	 * 获取单例实例
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Cache")
	static UImageCacheManager* GetInstance();

	/**
	 * 从缓存获取纹理，如果不存在则创建并缓存
	 * @param ImageData Base64编码的图片数据
	 * @param ImageFormat 图片格式
	 * @return 纹理对象
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Cache")
	UTexture2D* GetOrCreateTexture(const FString& ImageData, const FString& ImageFormat);

	/**
	 * 清除缓存
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Cache")
	void ClearCache();

	/**
	 * 获取缓存大小
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Cache")
	int32 GetCacheSize() const;

	/**
	 * 设置最大缓存数量
	 */
	UFUNCTION(BlueprintCallable, Category = "Image Cache")
	void SetMaxCacheSize(int32 MaxSize);

private:
	// 单例实例
	static UImageCacheManager* Instance;

	// 纹理缓存映射 (ImageData Hash -> Texture)
	UPROPERTY()
	TMap<FString, UTexture2D*> TextureCache;

	// 最大缓存数量
	int32 MaxCacheSize = 50;

	// 生成图片数据的哈希值
	FString GenerateImageHash(const FString& ImageData, const FString& ImageFormat) const;

	// 清理最旧的缓存项
	void CleanupOldestCache();
};
