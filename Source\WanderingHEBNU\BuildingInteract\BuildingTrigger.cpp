// Fill out your copyright notice in the Description page of Project Settings.


#include "BuildingTrigger.h"

#include "DTReader.h"
#include "Components/BoxComponent.h"
#include "Components/TextBlock.h"
#include "Kismet/GameplayStatics.h"


// Sets default values
ABuildingTrigger::ABuildingTrigger()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
	// CubeMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CubeMesh"));
	// RootComponent = CubeMesh;

	// static ConstructorHelpers::FObjectFinder<UStaticMesh> CubeMeshAsset(TEXT("/Engine/BasicShapes/Cube"));
	// if (CubeMeshAsset.Succeeded())
	// {
	// 	CubeMesh->SetStaticMesh(CubeMeshAsset.Object);
	// }

	TriggerBox = CreateDefaultSubobject<UBoxComponent>(TEXT("TriggerBox"));
	// RootComponent = TriggerBox;
	SetRootComponent(TriggerBox);
	// TriggerBox->SetupAttachment(RootComponent);
	TriggerBox->SetBoxExtent(FVector(100.0f, 100.0f, 100.0f));
	TriggerBox->OnComponentBeginOverlap.AddDynamic(this, &ABuildingTrigger::OnOverlapBegin);
	TriggerBox->OnComponentEndOverlap.AddDynamic(this, &ABuildingTrigger::OnOverlapEnd);
}

// Called when the game starts or when spawned
void ABuildingTrigger::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ABuildingTrigger::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void ABuildingTrigger::OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	if (OtherActor && (OtherActor != this) && OtherComp)
	{
		DTReader = Cast<ADTReader>(UGameplayStatics::GetActorOfClass(GetWorld(), ADTReader::StaticClass()));
		if (DTReader && DTReader->BuildingInfoTable)
		{
			FString BuildingInfo = DTReader->FindInfo(RowName);
			if (BuildingInfo != "" && BuildingInfoWidgetClass)
			{
				// 创建Widget实例并添加到视口
				BuildingInfoWidgetInstance = CreateWidget<UUserWidget>(GetWorld(), BuildingInfoWidgetClass);
				if (BuildingInfoWidgetInstance)
				{
					BuildingInfoWidgetInstance->AddToViewport();
 
					// 查找Widget中的Text Block并更新文本
					UTextBlock* TextBlock = Cast<UTextBlock>(BuildingInfoWidgetInstance->GetWidgetFromName(TEXT("TextBlock_BuildingInfo")));
					if (TextBlock)
					{
						TextBlock->SetText(FText::FromString(BuildingInfo));
					}
				}
			}
		}
	}
}

void ABuildingTrigger::OnOverlapEnd(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	if (OtherActor && (OtherActor != this) && OtherComp && BuildingInfoWidgetInstance)
	{
		BuildingInfoWidgetInstance->RemoveFromParent();
		BuildingInfoWidgetInstance = nullptr;
	}
}