#include "LetterObject.h"

ALetterObject::ALetterObject()
{
	// 设置此 Actor 在每帧调用 Tick()
	PrimaryActorTick . bCanEverTick = false;

	// 创建静态网格体组件
	Mesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Mesh"));
	SetRootComponent(Mesh);
}

void ALetterObject::BeginPlay()
{
	Super::BeginPlay();
}

void ALetterObject::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void ALetterObject::SetLetterContent(const FString& Content)
{
	LetterContent = Content;
}

FString ALetterObject::GetLetterContent()
{
	return LetterContent;
}

void ALetterObject::SetLetterStyleIndex(int Index)
{
	LetterStyleIndex = Index;
}

void ALetterObject::SetLetterLocation(const FVector& Location)
{
	LetterLocation = Location;
}

void ALetterObject::SetLetterRotation(const FRotator& Rotation)
{
	LetterRotation = Rotation;
}

void ALetterObject::SetLetterImageData(const FString& ImageData)
{
	LetterImageData = ImageData;
}

FString ALetterObject::GetLetterImageData() const
{
	return LetterImageData;
}

void ALetterObject::SetLetterImageFormat(const FString& ImageFormat)
{
	LetterImageFormat = ImageFormat;
}

FString ALetterObject::GetLetterImageFormat() const
{
	return LetterImageFormat;
}

bool ALetterObject::HasLetterImage() const
{
	return !LetterImageData.IsEmpty() && !LetterImageFormat.IsEmpty();
}
