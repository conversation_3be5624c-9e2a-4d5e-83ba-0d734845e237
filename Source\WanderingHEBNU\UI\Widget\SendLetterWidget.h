#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "SendLetterWidget.generated.h"

class UMultiLineEditableText;
class UImage;
class UTexture2D;

UCLASS()
class USendLetterWidget : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	TObjectPtr<UButton> GetGenerateButton() const;

	TObjectPtr<UButton> GetCancelButton() const;

	FString GetLetterContent() const;

	// 图片相关方法
	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	void OnSelectImageClicked();

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	void OnRemoveImageClicked();

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	void SetSelectedImage(const FString& ImagePath);

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	bool HasSelectedImage() const;

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	FString GetSelectedImagePath() const;

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	void ClearSelectedImage();

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "标题显示", meta = (BindWidget))
	TObjectPtr<UTextBlock> HeadText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "信件内容", meta = (BindWidget))
	TObjectPtr<UMultiLineEditableText> LetterContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "生成按钮", meta = (BindWidget))
	TObjectPtr<UButton> GenerateButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "取消按钮", meta = (BindWidget))
	TObjectPtr<UButton> CancelButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "生成文字", meta = (BindWidget))
	TObjectPtr<UTextBlock> GenerateText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "取消文字", meta = (BindWidget))
	TObjectPtr<UTextBlock> CancelText;

	// 图片相关UI组件
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "选择图片按钮", meta = (BindWidget))
	TObjectPtr<UButton> SelectImageButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "移除图片按钮", meta = (BindWidget))
	TObjectPtr<UButton> RemoveImageButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "图片预览", meta = (BindWidget))
	TObjectPtr<UImage> ImagePreview;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "图片信息文本", meta = (BindWidget))
	TObjectPtr<UTextBlock> ImageInfoText;

private:
	// 选中的图片路径
	FString SelectedImagePath;

	// 预览纹理
	UPROPERTY()
	TObjectPtr<UTexture2D> PreviewTexture;
};
