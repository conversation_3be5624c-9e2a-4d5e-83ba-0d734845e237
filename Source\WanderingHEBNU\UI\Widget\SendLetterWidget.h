#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "SendLetterWidget.generated.h"

class UMultiLineEditableText;

UCLASS()
class USendLetterWidget : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	TObjectPtr<UButton> GetGenerateButton() const;
	
	TObjectPtr<UButton> GetCancelButton() const;

	FString GetLetterContent() const;
	
protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "标题显示", meta = (BindWidget))
	TObjectPtr<UTextBlock> HeadText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "信件内容", meta = (BindWidget))
	TObjectPtr<UMultiLineEditableText> LetterContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "生成按钮", meta = (BindWidget))
	TObjectPtr<UButton> GenerateButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "取消按钮", meta = (BindWidget))
	TObjectPtr<UButton> CancelButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "生成文字", meta = (BindWidget))
	TObjectPtr<UTextBlock> GenerateText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "取消文字", meta = (BindWidget))
	TObjectPtr<UTextBlock> CancelText;
};
