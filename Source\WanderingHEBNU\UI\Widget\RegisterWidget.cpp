#include "RegisterWidget.h"

TObjectPtr<UButton> URegisterWidget::GetRegisterButton() const
{
	return RegisterButton;
}

TObjectPtr<UButton> URegisterWidget::GetBackButton() const
{
	return BackButton;
}

FString URegisterWidget::GetStudentIDData() const
{
	return StudentIDEditableTextBox->GetText().ToString();
}

FString URegisterWidget::GetPasswordData() const
{
	return PasswordEditableTextBox->GetText().ToString();
}

FString URegisterWidget::GetConfirmPasswordData() const
{
	return ConfirmPasswordEditableTextBox->GetText().ToString();
}

void URegisterWidget::OnRegisterClicked()
{

}
