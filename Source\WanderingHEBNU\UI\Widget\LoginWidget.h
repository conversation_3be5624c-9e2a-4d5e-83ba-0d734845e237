#pragma once

#include "CoreMinimal.h"
#include "Components/EditableTextBox.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "LoginWidget.generated.h"

UCLASS()
class ULoginWidget : public UBaseUserWidget
{
	GENERATED_BODY()
	
public:

	TObjectPtr<UButton> GetLoginButton() const;
	
	TObjectPtr<UButton> GetExitButton() const;

	int GetAccountData() const;

	FString GetPasswordData() const;
	
protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "登录按钮", meta = (BindWidget))
	TObjectPtr<UButton> LoginButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "退出按钮", meta = (BindWidget))
	TObjectPtr<UButton> ExitButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "账号文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> AccountEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "密码文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> PasswordEditableTextBox;

	UFUNCTION(BlueprintCallable, Category = "登录操作")
	void OnLoginClicked();
	
};
