#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "ReadLetterWidget.generated.h"

class UMultiLineEditableText;

UCLASS()
class UReadLetterWidget : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	TObjectPtr<UButton> GetCloseButton() const;

	void SetLetterContent(const FString& Content) const;
	
protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "信件内容", meta = (BindWidget))
	TObjectPtr<UMultiLineEditableText> LetterContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "关闭按钮", meta = (BindWidget))
	TObjectPtr<UButton> CloseButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "关闭文字", meta = (BindWidget))
	TObjectPtr<UTextBlock> CloseText;
};

