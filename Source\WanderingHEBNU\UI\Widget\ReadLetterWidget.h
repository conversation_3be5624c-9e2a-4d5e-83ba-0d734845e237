#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "ReadLetterWidget.generated.h"

class UMultiLineEditableText;
class UImage;
class UTexture2D;

UCLASS()
class UReadLetterWidget : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	TObjectPtr<UButton> GetCloseButton() const;

	void SetLetterContent(const FString& Content) const;

	// 图片相关方法
	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	void SetLetterImage(const FString& ImageData, const FString& ImageFormat);

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	void ClearLetterImage();

	UFUNCTION(BlueprintCallable, Category = "Letter Image")
	bool HasLetterImage() const;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "信件内容", meta = (BindWidget))
	TObjectPtr<UMultiLineEditableText> LetterContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "关闭按钮", meta = (BindWidget))
	TObjectPtr<UButton> CloseButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "关闭文字", meta = (BindWidget))
	TObjectPtr<UTextBlock> CloseText;

	// 图片显示组件
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "信件图片", meta = (BindWidget))
	TObjectPtr<UImage> LetterImage;

private:
	// 当前显示的图片纹理
	UPROPERTY()
	TObjectPtr<UTexture2D> CurrentImageTexture;
};

