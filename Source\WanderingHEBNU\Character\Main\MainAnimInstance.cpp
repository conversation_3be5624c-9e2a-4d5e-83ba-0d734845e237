#include "MainAnimInstance.h"

#include "GameFramework/PawnMovementComponent.h"
#include "../../Common/Log.h"
#include "MainCharacter.h"

void UMainAnimInstance::NativeUpdateAnimation(float DeltaSeconds)
{
	Super::NativeUpdateAnimation(DeltaSeconds);
	const AMainCharacter* Owner = Cast<AMainCharacter>(TryGetPawnOwner());
	if (!Owner) { return; }
	bIsInAir = Owner -> GetMovementComponent() -> IsFalling();
	const FVector Velocity = Owner -> GetVelocity();
	Speed = FMath::Max(FMath::Abs(Velocity . X), FMath::Abs(Velocity . Y));
}

void UMainAnimInstance::PlayWaveHandAnimation()
{
	if (WaveHandMontage)
	{
		Montage_Play(WaveHandMontage);
	}
}
