#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "MainGameMode.generated.h"

class UMainGameInstance;
class ANetManager;

UCLASS()
class AMainGameMode : public AGameModeBase
{
	GENERATED_BODY()

public:
	AMainGameMode();

	virtual void PostLogin(APlayerController* NewPlayer) override;

	virtual void BeginPlay() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	virtual void Tick(float DeltaSeconds) override;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "游戏模式设置", DisplayName = "网络管理类模版")
	TSubclassOf<ANetManager> NetManagerSubclassOf = nullptr;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "网络管理")
	TObjectPtr<ANetManager> NetManager = nullptr;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "游戏实例")
	TObjectPtr<UMainGameInstance> MainGameInstance = nullptr;
};
