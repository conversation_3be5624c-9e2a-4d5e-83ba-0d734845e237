#pragma once

#include "CoreMinimal.h"
#include "../Base/BaseUserWidget.h"
#include "GameHUD.generated.h"

UCLASS()
class UGameHUD : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	virtual void Open() override;

	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

	void SetUserInfo(const FString& Info) const;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "用户信息显示", meta = (BindWidget))
	TObjectPtr<UTextBlock> UserInfoText;
};
