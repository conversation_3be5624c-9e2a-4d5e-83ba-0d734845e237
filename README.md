# 云游师大 Wandering HEBNU

# 作者

```
 <AUTHOR>
```

# 克隆项目

```shell
# 克隆项目以及子模块
git clone --recursive *********************:6708806c1c80af57f902d17f/22-S6-AIP/WanderingHEBNU.git

# 当子模块不存在时执行
git submodule update --init --recursive
```

# 项目规范

> 代码规范通读：

```
https://dev.epicgames.com/documentation/zh-cn/unreal-engine/epic-cplusplus-coding-standard-for-unreal-engine?application_version=5.4
```

> 蓝图规范

## 文件放置

```
---|Content
------|Blueprint 存放蓝图类
------|Map 地图关卡
------|Mesh 模型、材质、贴图等资产
------|Music 音效文件
```

## 文件命名规范

### 蓝图文件加后缀（关卡地图例外）

> CPP派生

```
CPP派生出的蓝图统一加 _BP 后缀
例如有一个Cpp类名为MainCharacter
派生出的蓝图类文件名为 MainCharacter_BP
```

> 资产

```
以缩写作为后缀
例如：
    材质 Material Demo_M
    静态网格体 StaticMesh Demo_SM
    骨骼网格体 SkeletalMesh Demo_SKM
    以此类推
关卡地图资产例外，不需要加后缀
```