#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "MainMenuWidget.generated.h"

UCLASS()
class UMainMenuWidget : public UBaseUserWidget
{
	GENERATED_BODY()
public:
	virtual void Open() override;

	TObjectPtr<UButton> GetLoginButton() const;
	
	TObjectPtr<UButton> GetExitButton() const;

	TObjectPtr<UButton> GetRegisterButton() const;
	
	TObjectPtr<UButton> GetSettingButton() const;

	virtual void NativeConstruct() override;
	
protected:

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "登录按钮", meta = (BindWidget))
	TObjectPtr<UButton> LoginButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "退出按钮", meta = (BindWidget))
	TObjectPtr<UButton> ExitButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "注册按钮", meta = (BindWidget))
	TObjectPtr<UButton> RegisterButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "设置按钮", meta = (BindWidget))
	TObjectPtr<UButton> SettingButton;

	// UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "登录文字", meta = (BindWidget))
	// TObjectPtr<UTextBlock> LoginText;

	UFUNCTION(BlueprintCallable, Category = "登录操作")
	void OnExitClicked();

	

};
