#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "../../../Common/Log.h"
#include "TCPSocket.h"
#include "TCPSocketComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTCPSocketStartDelegate, const FString&, BoundIP, int, BoundPort);

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnTCPSocketCloseDelegate);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnTCPSocketReceivedMessageDelegate, const FString&, Message, const FString&, Endpoint, const int, Port);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class UTCPSocketComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UTCPSocketComponent();

	virtual void BeginPlay() override;

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	void Start() const;

	void Close() const;

	bool SendStream(const NetPacket::FStream& Stream) const;

	UFUNCTION(BlueprintCallable)
	bool Send(const FString& Message) const;

	FOnTCPSocketStartDelegate& GetOnStartDelegate() { return OnStartDelegate; }

	FOnTCPSocketCloseDelegate& GetOnCloseDelegate() { return OnCloseDelegate; }

	FOnTCPSocketReceivedMessageDelegate& GetOnReceivedMessageDelegate() { return OnReceivedMessageDelegate; }

	FOnTCPSocketReceivedMessageDelegate& GetOnReceivedMessageInGameThreadDelegate() { return OnReceivedMessageInGameThreadDelegate; }

protected:
	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "开始回调代理")
	FOnTCPSocketStartDelegate OnStartDelegate;

	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "关闭回调代理")
	FOnTCPSocketCloseDelegate OnCloseDelegate;

	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "接收消息回调代理（UDP线程）")
	FOnTCPSocketReceivedMessageDelegate OnReceivedMessageDelegate;

	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "接收消息回调代理（Game线程）")
	FOnTCPSocketReceivedMessageDelegate OnReceivedMessageInGameThreadDelegate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "连接设置")
	FTCPLink Setting;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "套接字")
	TObjectPtr<UTCPSocket> Socket;

	void BindDelegate() const;
};
