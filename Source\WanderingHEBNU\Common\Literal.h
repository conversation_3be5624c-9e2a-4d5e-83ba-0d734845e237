#pragma once

#include "CoreMinimal.h"

/**
 * 字面量替换
 */
class FLiteral
{
public:
#pragma region Collision

	// 不可见类型碰撞
	inline static ECollisionChannel ECC_Invisible = ECC_GameTraceChannel1;

#pragma endregion Collision


#pragma region Color

	/**
	 * 颜色常量
	 */
	inline static FLinearColor SunYellowColor = FColor(243, 159, 024);

	inline static FLinearColor SnowOneColor = FColor(255, 250, 250);

	inline static FLinearColor BlackColor = FColor(0, 0, 0);

	inline static FLinearColor WarningColor = FColor(112, 128, 105);

	inline static FLinearColor SuccessColor = FColor::Turquoise;

	inline static FLinearColor KleinBlueColor = FColor(0, 47, 167);

	inline static FLinearColor SchonbrunnerGelbColor = FColor(251, 210, 106);

	inline static FLinearColor BurgundyRedColor = FColor(71, 0, 36);

	inline static FLinearColor MarrsGreenColor = FColor(1, 132, 127);

	inline static FLinearColor VandykeBrownColor = FColor(73, 45, 34);

	inline static FLinearColor PrussianBlueColor = FColor(0, 49, 83);

	inline static FLinearColor TitianRedColor = FColor(212, 72, 72);

#pragma endregion Color
};
