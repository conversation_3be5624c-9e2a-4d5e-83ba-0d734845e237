#include "ImageCacheManager.h"
#include "ImageUtils.h"
#include "Misc/SecureHash.h"

// 静态实例定义
UImageCacheManager* UImageCacheManager::Instance = nullptr;

UImageCacheManager* UImageCacheManager::GetInstance()
{
	if (!Instance)
	{
		Instance = NewObject<UImageCacheManager>();
		Instance->AddToRoot(); // 防止被垃圾回收
	}
	return Instance;
}

UTexture2D* UImageCacheManager::GetOrCreateTexture(const FString& ImageData, const FString& ImageFormat)
{
	if (ImageData.IsEmpty() || ImageFormat.IsEmpty())
	{
		return nullptr;
	}

	// 生成缓存键
	FString CacheKey = GenerateImageHash(ImageData, ImageFormat);

	// 检查缓存中是否已存在
	if (UTexture2D** CachedTexture = TextureCache.Find(CacheKey))
	{
		if (*CachedTexture && IsValid(*CachedTexture))
		{
			UE_LOG(LogTemp, Log, TEXT("从缓存获取图片纹理: %s"), *CacheKey);
			return *CachedTexture;
		}
		else
		{
			// 缓存的纹理无效，移除
			TextureCache.Remove(CacheKey);
		}
	}

	// 创建新纹理
	UTexture2D* NewTexture = UImageUtils::Base64ToTexture2D(ImageData, ImageFormat);
	if (NewTexture)
	{
		// 检查缓存大小限制
		if (TextureCache.Num() >= MaxCacheSize)
		{
			CleanupOldestCache();
		}

		// 添加到缓存
		TextureCache.Add(CacheKey, NewTexture);
		UE_LOG(LogTemp, Log, TEXT("创建并缓存新图片纹理: %s"), *CacheKey);
	}

	return NewTexture;
}

void UImageCacheManager::ClearCache()
{
	TextureCache.Empty();
	UE_LOG(LogTemp, Log, TEXT("图片缓存已清空"));
}

int32 UImageCacheManager::GetCacheSize() const
{
	return TextureCache.Num();
}

void UImageCacheManager::SetMaxCacheSize(int32 MaxSize)
{
	MaxCacheSize = FMath::Max(1, MaxSize);
	
	// 如果当前缓存超过新的限制，清理多余的项
	while (TextureCache.Num() > MaxCacheSize)
	{
		CleanupOldestCache();
	}
}

FString UImageCacheManager::GenerateImageHash(const FString& ImageData, const FString& ImageFormat) const
{
	// 使用MD5生成哈希值
	FString CombinedData = ImageFormat + TEXT("_") + ImageData.Left(1000); // 只使用前1000个字符来生成哈希
	return FMD5::HashAnsiString(*CombinedData);
}

void UImageCacheManager::CleanupOldestCache()
{
	if (TextureCache.Num() > 0)
	{
		// 简单实现：移除第一个找到的项
		// 在实际项目中，可以实现LRU算法
		auto Iterator = TextureCache.CreateIterator();
		if (Iterator)
		{
			FString KeyToRemove = Iterator.Key();
			Iterator.RemoveCurrent();
			UE_LOG(LogTemp, Log, TEXT("清理缓存项: %s"), *KeyToRemove);
		}
	}
}
