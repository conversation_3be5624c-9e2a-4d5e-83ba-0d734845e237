#include "GuestCharacter.h"

#include "../../UI/Widget/HeadInfoWidget.h"
#include "../../Common/Literal.h"

AGuestCharacter::AGuestCharacter()
{
	PrimaryActorTick . bCanEverTick = true;
	HeadWidgetComponent = CreateDefaultSubobject<UWidgetComponent>(TEXT("HeadWidgetComponent"));
	HeadWidgetComponent -> SetWidgetSpace(EWidgetSpace::Screen);
	HeadWidgetComponent -> SetVisibility(true);
	HeadWidgetComponent -> SetHiddenInGame(false);
	HeadWidgetComponent -> SetCollisionObjectType(FLiteral::ECC_Invisible);
	HeadWidgetComponent -> SetupAttachment(GetRootComponent());
}

void AGuestCharacter::BeginPlay()
{
	Super::BeginPlay();
}

void AGuestCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	NetMotionReplicate(DeltaTime);
}

void AGuestCharacter::SetUserID(const int Value)
{
	UserID = Value;
	if (const UHeadInfoWidget* Widget = Cast<UHeadInfoWidget>(HeadWidgetComponent -> GetWidget()))
	{
		const FString Info = TEXT("UID: ") + FString::FromInt(UserID);
		Widget -> SetInfo(Info);
	}
}

UGuestAnimInstance* AGuestCharacter::GetAnimInstance()
{
	if (AnimInstance) { return Cast<UGuestAnimInstance>(AnimInstance); }
	AnimInstance = Cast<UGuestAnimInstance>(GetMesh() -> GetAnimInstance());
	return Cast<UGuestAnimInstance>(AnimInstance);
}

void AGuestCharacter::NetMotionReplicate(const float DeltaTime)
{
	UGuestAnimInstance* Instance = GetAnimInstance();
	const FVector CurrentLocation = GetActorLocation();
	const FVector TargetLocation = TargetMotion . GetMotion() . GetLocation();
	const FRotator CurrentRotation = GetActorRotation();
	const FRotator TargetRotation = TargetMotion . GetMotion() . GetRotation() . Rotator();
	const float Distance = FVector::Dist(CurrentLocation, TargetLocation);
	const float PlaneDistance = FVector2D::Distance(FVector2D(CurrentLocation), FVector2D(TargetLocation));
	if (Distance >= NetMotionRepTolerance)
	{
		const FVector Lerp = FMath::VInterpTo(CurrentLocation, TargetLocation, DeltaTime, NetMotionRepSmooth);
		SetActorLocation(Lerp);
	}
	if (
		FMath::Abs(FRotator::NormalizeAxis(CurrentRotation . Pitch - TargetRotation . Pitch)) >= NetMotionRepTolerance ||
		FMath::Abs(FRotator::NormalizeAxis(CurrentRotation . Yaw - TargetRotation . Yaw)) >= NetMotionRepTolerance ||
		FMath::Abs(FRotator::NormalizeAxis(CurrentRotation . Roll - TargetRotation . Roll)) >= NetMotionRepTolerance
	)
	{
		const FRotator Lerp = FMath::RInterpTo(CurrentRotation, TargetRotation, DeltaTime, NetMotionRepSmooth);
		SetActorRotation(Lerp);
	}
	if (Instance)
	{
		float Speed = 0;
		if (PlaneDistance >= NetMotionRepTolerance)
		{
			Speed = NetMotionSpeed;
			if (Distance <= NetMotionRepAnimTolerance)
			{
				Speed = FMath::Lerp(
					0, NetMotionSpeed / NetMotionSpeedBrake,
					Distance / NetMotionRepAnimTolerance
				);
			}
		}
		Instance -> SetIsInAir(TargetMotion . IsInAir());
		Instance -> SetIsWaving(TargetMotion . IsWaving());
		Instance -> SetSpeed(Speed);
		
		if (TargetMotion.IsWaving())
		{
			Instance->PlayWaveHandAnimation();
		}
	}
}
