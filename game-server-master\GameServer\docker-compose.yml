services:
  mysql:
    container_name: gs-mysql
    image: mysql/mysql-server:8.0.4
    command: [ "mysqld", "--default-authentication-plugin=mysql_native_password" ]
    environment:
      MYSQL_ROOT_HOST: '%'
      MYSQL_DATABASE: db
      MYSQL_ROOT_PASSWORD: 123456
    ports:
      - "3310:3306"

  go:
    container_name: gs-go
    build:
      context: docker/go
      dockerfile: Dockerfile
    volumes:
      - .:/www:delegated
    working_dir: /www
    depends_on:
      - mysql
    ports:
      - "10000:10000/udp"
      - "10001:10001/udp"
    tty: true
    command: >
      bash -c "go run Main.go"
