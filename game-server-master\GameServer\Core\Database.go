package Core

import (
	// "database/sql"
	"fmt"
	// _ "github.com/go-sql-driver/mysql"
	// "log"
	"time"
	"gorm.io/driver/mysql"
  	"gorm.io/gorm"
)

// var DB *sql.DB
var db *gorm.DB

type Letter struct {
	LetterID int64 `gorm:"primaryKey;autoIncrement"`
	UserID int64
	Content string
	StyleIndex int64
	Location string
	Rotation string
	CreateTime time.Time `gorm:"autoCreateTime;type:timestamp"`
	// 图片相关字段
	ImageData string `gorm:"type:longtext"` // Base64编码的图片数据
	ImageFormat string `gorm:"type:varchar(10)"` // 图片格式：JPG/PNG/BMP
	ImageSize int64 `gorm:"default:0"` // 原始图片大小（字节）
}

// 学生白名单表
type Student struct {
	StudentID string `gorm:"primaryKey;type:varchar(20)"`
	Name string `gorm:"type:varchar(50);not null"`
	CreateTime time.Time `gorm:"autoCreateTime;type:timestamp"`
}

// 用户表
type User struct {
	UserID int64 `gorm:"primaryKey;autoIncrement"`
	StudentID string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	CreateTime time.Time `gorm:"autoCreateTime;type:timestamp"`
	LastLoginTime *time.Time `gorm:"type:timestamp"`
}

func InitDatabase() error {
	dsn := "root:@tcp(localhost:3306)/game_db?charset=utf8mb4&parseTime=True&loc=Local"
	var err error
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	fmt.Println(db,err)
	db.AutoMigrate(&Letter{}, &Student{}, &User{})
	// var err error
	// DB, err = sql.Open("mysql", "root:@tcp(localhost:3306)/game_db?charset=utf8mb4&parseTime=True&loc=Local")
	// if err != nil {
	// 	return fmt.Errorf("连接数据库失败: %v", err)
	// }

	// // 测试连接
	// err = DB.Ping()
	// if err != nil {
	// 	return fmt.Errorf("数据库连接测试失败: %v", err)
	// }
	return nil
}

func SaveLetter(userID int, content string, styleIndex int, location string, rotation string) (Letter, error) {
	// _, err := DB.Exec("INSERT INTO letters (content, style_index, location, rotation) VALUES (?, ?, ?, ?)",
	// 	content, styleIndex, location, rotation)
	// if err != nil {
	// 	return fmt.Errorf("保存信件失败: %v", err)
	// }
	letter := Letter{UserID:int64(userID),Content:content,StyleIndex:int64(styleIndex),Location:location,Rotation:rotation}
	db.Create(&letter)
	return letter, nil
}

// 保存带图片的信件
func SaveLetterWithImage(userID int, content string, styleIndex int, location string, rotation string, imageData string, imageFormat string, imageSize int64) (Letter, error) {
	if db == nil {
		return Letter{}, fmt.Errorf("数据库未初始化")
	}

	// 验证图片格式
	if imageFormat != "" && imageFormat != "JPG" && imageFormat != "PNG" && imageFormat != "BMP" {
		return Letter{}, fmt.Errorf("不支持的图片格式: %s", imageFormat)
	}

	// 验证图片大小（2MB限制）
	if imageSize > 2*1024*1024 {
		return Letter{}, fmt.Errorf("图片大小超过限制: %d bytes", imageSize)
	}

	letter := Letter{
		UserID: int64(userID),
		Content: content,
		StyleIndex: int64(styleIndex),
		Location: location,
		Rotation: rotation,
		ImageData: imageData,
		ImageFormat: imageFormat,
		ImageSize: imageSize,
	}

	result := db.Create(&letter)
	if result.Error != nil {
		return Letter{}, fmt.Errorf("保存信件失败: %v", result.Error)
	}

	return letter, nil
}

func GetLatestLetters(limit int) ([]Letter, error) {
	if db == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	var letters []Letter
	result := db.Order("letter_id DESC").Limit(limit).Find(&letters)
	if result.Error != nil {
		return nil, fmt.Errorf("查询信件失败: %v", result.Error)
	}
	return letters, nil
}

func CheckDailyLetterLimit(userID int, maxCount int) (bool, error) {
    if db == nil {
        return false, fmt.Errorf("数据库未初始化")
    }

    // 获取当前日期（UTC时区，格式：YYYY-MM-DD）
    todayUTC := time.Now().UTC().Format("2006-01-02")

    var count int64
    // 使用数据库方言特定的日期处理函数
    result := db.Model(&Letter{}).
        Where("user_id = ? AND create_time >= ?",
             userID,
             todayUTC+" 00:00:00").
        Where("create_time < ?",
             tomorrowUTC()).
        Count(&count)

    if result.Error != nil {
        return false, fmt.Errorf("查询信件数量失败: %v", result.Error)
    }

    return count >= int64(maxCount), nil
}

func tomorrowUTC() string {
    tomorrow := time.Now().UTC().AddDate(0, 0, 1)
    return tomorrow.Format("2006-01-02") + " 00:00:00"
}

// 检查学号是否在白名单中
func IsStudentInWhitelist(studentID string) (bool, error) {
	if db == nil {
		return false, fmt.Errorf("数据库未初始化")
	}

	var count int64
	result := db.Model(&Student{}).Where("student_id = ?", studentID).Count(&count)
	if result.Error != nil {
		return false, fmt.Errorf("查询白名单失败: %v", result.Error)
	}

	return count > 0, nil
}

// 检查用户是否已存在
func IsUserExists(studentID string) (bool, error) {
	if db == nil {
		return false, fmt.Errorf("数据库未初始化")
	}

	var count int64
	result := db.Model(&User{}).Where("student_id = ?", studentID).Count(&count)
	if result.Error != nil {
		return false, fmt.Errorf("查询用户失败: %v", result.Error)
	}

	return count > 0, nil
}

// 注册新用户
func RegisterUser(studentID, password string) (*User, error) {
	if db == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	// 检查白名单
	inWhitelist, err := IsStudentInWhitelist(studentID)
	if err != nil {
		return nil, err
	}
	if !inWhitelist {
		return nil, fmt.Errorf("学号不在白名单中")
	}

	// 检查用户是否已存在
	exists, err := IsUserExists(studentID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, fmt.Errorf("用户已存在")
	}

	// 创建新用户
	user := User{
		StudentID: studentID,
		Password:  password, // 注意：实际项目中应该对密码进行哈希处理
	}

	result := db.Create(&user)
	if result.Error != nil {
		return nil, fmt.Errorf("创建用户失败: %v", result.Error)
	}

	return &user, nil
}

// 验证用户登录
func ValidateUser(studentID, password string) (*User, error) {
	if db == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}

	var user User
	result := db.Where("student_id = ? AND password = ?", studentID, password).First(&user)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户名或密码错误")
		}
		return nil, fmt.Errorf("验证用户失败: %v", result.Error)
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginTime = &now
	db.Save(&user)

	return &user, nil
}

// 添加学生到白名单（用于测试）
func AddStudentToWhitelist(studentID, name string) error {
	if db == nil {
		return fmt.Errorf("数据库未初始化")
	}

	student := Student{
		StudentID: studentID,
		Name:      name,
	}

	result := db.Create(&student)
	if result.Error != nil {
		return fmt.Errorf("添加学生到白名单失败: %v", result.Error)
	}

	return nil
}