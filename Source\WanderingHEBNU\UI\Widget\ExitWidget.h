#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "ExitWidget.generated.h"

UCLASS()
class UExitWidget : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	UFUNCTION()
	void OnExitClicked();
	
	virtual void NativeConstruct() override;

	UButton* GetCancelButton() const { return CancelButton; }

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "确认按钮", meta = (BindWidget))
	TObjectPtr<UButton> ConfirmButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "取消按钮", meta = (BindWidget))
	TObjectPtr<UButton> CancelButton;
};
