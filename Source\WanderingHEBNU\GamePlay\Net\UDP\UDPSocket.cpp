#include "UDPSocket.h"

#include "Common/UdpSocketBuilder.h"
#include "Common/UdpSocketReceiver.h"
#include "../../../Common/Log.h"

bool UUDPSocket::Start(const FUDPLink& Link)
{
	if (!SocketSubsystem) { SocketSubsystem = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM); }
	const FString IP = Link . ServerIP;
	const int Port = Link . ServerPort;
	const int BufferSize = Link . BufferSize;
	RemoteAddress = SocketSubsystem -> CreateInternetAddr();
	bool bIsValid;
	RemoteAddress -> SetIp(*IP, bIsValid);
	RemoteAddress -> SetPort(Port);
	if (!bIsValid)
	{
		ULog::Info(TEXT("UDP: 地址无效 " + IP + ":" + FString::FromInt(Port)));
		return false;
	}
	const FIPv4Endpoint Local = FIPv4Endpoint(FIPv4Address::Any, Port + 1);
	Socket = FUdpSocketBuilder("UDPSocket")
	         . AsReusable()
	         . WithBroadcast()
	         . BoundToEndpoint(Local)
	         . AsNonBlocking()
	         . WithSendBufferSize(BufferSize)
	         . WithReceiveBufferSize(BufferSize)
	         . Build();
	if (!Socket) { return PrintError(); }
	const FTimespan ThreadWaitTime = FTimespan::FromMilliseconds(100);
	const FString ThreadName = TEXT("Receiver");
	Receiver = new FUdpSocketReceiver(Socket, ThreadWaitTime, *ThreadName);
	Receiver -> OnDataReceived() . BindLambda([this](const FArrayReaderPtr& Data, const FIPv4Endpoint& Endpoint)
	{
		TArray<uint8> Raw;
		Raw . Append(Data -> GetData(), Data -> Num());
		Raw . Add(0);
		const FString Message = FString(UTF8_TO_TCHAR(reinterpret_cast<const char*>(Raw . GetData())));
		const FString SenderIP = Endpoint . Address . ToString();
		int SenderPort = Endpoint . Port;
		if (OnReceivedMessage) { OnReceivedMessage(Message, SenderIP, SenderPort); }
		if (OnReceivedMessageInGameThread)
		{
			AsyncTask(ENamedThreads::GameThread, [this, Message, SenderIP, SenderPort]()
			{
				if (OnReceivedMessageInGameThread) { OnReceivedMessageInGameThread(Message, SenderIP, SenderPort); }
			});
		}
	});
	Receiver -> Start();
	if (OnSocketStart)
	{
		const TSharedRef<FInternetAddr> BoundIP = SocketSubsystem -> CreateInternetAddr();
		Socket -> GetAddress(*BoundIP);
		OnSocketStart(BoundIP -> ToString(false), Socket -> GetPortNo());
	}
	return true;
}

bool UUDPSocket::Close()
{
	bool bResult = true;
	if (Receiver)
	{
		Receiver -> Stop();
		delete Receiver;
	}
	if (Socket)
	{
		bResult = Socket -> Close();
		SocketSubsystem -> DestroySocket(Socket);
	}
	if (OnSocketClose) { OnSocketClose(); }
	Socket = nullptr;
	Receiver = nullptr;
	RemoteAddress = nullptr;
	SocketSubsystem = nullptr;
	OnSocketStart = nullptr;
	OnSocketClose = nullptr;
	OnReceivedMessage = nullptr;
	OnReceivedMessageInGameThread = nullptr;
	return bResult;
}

bool UUDPSocket::SendStream(NetPacket::FStream Stream) const
{
	return Send(Stream . ToString());
}

bool UUDPSocket::Send(const FString& Message) const
{
	TArray<uint8> Bytes;
	TArray<TCHAR> Array = Message . GetCharArray();
	Bytes . Append(reinterpret_cast<uint8*>(TCHAR_TO_UTF8(Array . GetData())), Array . Num());
	return SendBytes(Bytes);
}

bool UUDPSocket::SendBytes(const TArray<uint8>& Bytes) const
{
	if (!Socket || Socket -> GetConnectionState() != SCS_Connected) { return false; }
	int32 BytesSent = 0;
	bool bResult = Socket -> SendTo(Bytes . GetData(), Bytes . Num(), BytesSent, *RemoteAddress);
	if (!bResult) { bResult = PrintError(); }
	return bResult;
}

bool UUDPSocket::PrintError() const
{
	if (!SocketSubsystem) { return false; }
	const ESocketErrors ErrorCode = SocketSubsystem -> GetLastErrorCode();
	const FString Error = SocketSubsystem -> GetSocketError(ErrorCode);
	ULog::Info(TEXT("UDP: 错误码 ") + Error);
	return false;
}
