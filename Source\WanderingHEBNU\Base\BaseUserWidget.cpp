#include "BaseUserWidget.h"

void UBaseUserWidget::Open()
{
	SetIsFocusable(true);
	SetKeyboardFocus();
	SetVisibility(ESlateVisibility::Visible);
}

void UBaseUserWidget::Close()
{
	RemoveFromParent();
}

void UBaseUserWidget::SetInputUI()
{
	if (!Controller || !<PERSON><PERSON><PERSON><PERSON>(Controller)) { GetController(); }
	if (!Controller || !<PERSON><PERSON>ali<PERSON>(Controller)) { return; }
	GetController() -> SetInputMode(FInputModeUIOnly());
	GetController() -> SetShowMouseCursor(true);
}

void UBaseUserWidget::SetInputGame()
{
	if (!Controller || !IsValid(Controller)) { GetController(); }
	if (!Controller || !<PERSON><PERSON>ali<PERSON>(Controller)) { return; }
	GetController() -> SetInputMode(FInputModeGameOnly());
	GetController() -> SetShowMouseCursor(false);
}
