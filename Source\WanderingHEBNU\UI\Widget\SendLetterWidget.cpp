#include "SendLetterWidget.h"

#include "Components/MultiLineEditableText.h"
#include "Components/Button.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Engine/Texture2D.h"
#include "WanderingHEBNU/Common/ImageUtils.h"
#include "HAL/PlatformFilemanager.h"
#include "Developer/DesktopPlatform/Public/IDesktopPlatform.h"
#include "Developer/DesktopPlatform/Public/DesktopPlatformModule.h"

TObjectPtr<UButton> USendLetterWidget::GetGenerateButton() const
{
	return GenerateButton;
}

TObjectPtr<UButton> USendLetterWidget::GetCancelButton() const
{
	return CancelButton;
}

FString USendLetterWidget::GetLetterContent() const
{
	return LetterContent -> GetText().ToString();
}

void USendLetterWidget::OnSelectImageClicked()
{
	// 获取桌面平台模块
	IDesktopPlatform* DesktopPlatform = FDesktopPlatformModule::Get();
	if (!DesktopPlatform)
	{
		UE_LOG(LogTemp, Warning, TEXT("无法获取桌面平台模块"));
		return;
	}

	// 设置文件对话框参数
	TArray<FString> OutFileNames;
	const FString FileTypes = TEXT("Image Files|*.jpg;*.jpeg;*.png;*.bmp");
	const FString DefaultPath = FPaths::ProjectDir();

	// 打开文件选择对话框
	bool bOpened = DesktopPlatform->OpenFileDialog(
		nullptr,
		TEXT("选择图片文件"),
		DefaultPath,
		TEXT(""),
		FileTypes,
		EFileDialogFlags::None,
		OutFileNames
	);

	if (bOpened && OutFileNames.Num() > 0)
	{
		SetSelectedImage(OutFileNames[0]);
	}
}

void USendLetterWidget::OnRemoveImageClicked()
{
	ClearSelectedImage();
}

void USendLetterWidget::SetSelectedImage(const FString& ImagePath)
{
	// 验证文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*ImagePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("图片文件不存在: %s"), *ImagePath);
		return;
	}

	// 验证图片格式和大小
	FString Format;
	int64 Size;
	FString Base64;

	if (!UImageUtils::ImageFileToBase64(ImagePath, Base64, Format, Size))
	{
		UE_LOG(LogTemp, Warning, TEXT("图片验证失败: %s"), *ImagePath);
		return;
	}

	// 保存图片路径
	SelectedImagePath = ImagePath;

	// 创建预览纹理
	PreviewTexture = UImageUtils::Base64ToTexture2D(Base64, Format);
	if (PreviewTexture && ImagePreview)
	{
		ImagePreview->SetBrushFromTexture(PreviewTexture);
		ImagePreview->SetVisibility(ESlateVisibility::Visible);
	}

	// 更新UI状态
	if (RemoveImageButton)
	{
		RemoveImageButton->SetVisibility(ESlateVisibility::Visible);
	}

	if (ImageInfoText)
	{
		FString InfoText = FString::Printf(TEXT("格式: %s | 大小: %.1f KB"),
			*Format, Size / 1024.0f);
		ImageInfoText->SetText(FText::FromString(InfoText));
		ImageInfoText->SetVisibility(ESlateVisibility::Visible);
	}

	UE_LOG(LogTemp, Log, TEXT("已选择图片: %s"), *ImagePath);
}

bool USendLetterWidget::HasSelectedImage() const
{
	return !SelectedImagePath.IsEmpty();
}

FString USendLetterWidget::GetSelectedImagePath() const
{
	return SelectedImagePath;
}

void USendLetterWidget::ClearSelectedImage()
{
	SelectedImagePath.Empty();
	PreviewTexture = nullptr;

	// 隐藏图片相关UI
	if (ImagePreview)
	{
		ImagePreview->SetVisibility(ESlateVisibility::Hidden);
	}

	if (RemoveImageButton)
	{
		RemoveImageButton->SetVisibility(ESlateVisibility::Hidden);
	}

	if (ImageInfoText)
	{
		ImageInfoText->SetVisibility(ESlateVisibility::Hidden);
	}

	UE_LOG(LogTemp, Log, TEXT("已清除选中的图片"));
}