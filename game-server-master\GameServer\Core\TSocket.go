package Core

import (
	"fmt"
	"github.com/goccy/go-json"
	"github.com/panjf2000/gnet/v2"
	"log"
	"time"
)

type TSocket struct {
	gnet.BuiltinEventEngine
	Engine gnet.Engine
}

func (Self *TSocket) OnBoot(Engine gnet.Engine) gnet.Action {
	Self.Engine = Engine
	log.Printf("TCP服务器初始化")
	return gnet.None
}

func (Self *TSocket) OnTraffic(Connect gnet.Conn) gnet.Action {
	RawData, _ := Connect.Next(-1)
	Stream := NewNStream()
	log.Printf("TCP消息 %s\n", RawData)
	if Error := json.Unmarshal(RawData, &Stream); Error != nil {
		log.Println("Error Message : " + string(RawData))
		log.Printf("Error : %v \n", Error)
		return gnet.None
	}
	if Value, OK := Pool.Load(Stream.UserID); OK {
		Client := Value.(*NClient)
		Client.TCP.Connection = Connect
		Client.TCP.LastActive = time.Now()
	} else {
		Client := &NClient{
			TCP: TPack{
				Connection: Connect,
				LastActive: time.Now(),
			},
		}
		Pool.Store(Stream.UserID, Client)
	}
	Self.HandleStream(Connect, Stream)
	return gnet.None
}

func (Self *TSocket) Cleanup() {
	Ticker := time.NewTicker(CleanupInterval)
	defer Ticker.Stop()
	for range Ticker.C {
		Now := time.Now()
		Pool.Range(func(Key, Value interface{}) bool {
			Client := Value.(*NClient)
			if Now.Sub(Client.TCP.LastActive) > InactiveTimeout {
				Self.CleanUser(Key.(int))
				log.Printf("用户 %d TCP连接过期\n", Key.(int))
			}
			return true
		})
	}
}

func (Self *TSocket) CleanUser(UserID int) {
	Pool.Delete(UserID)
	Stream := NewNStream()
	Stream.UserID = UserID
	Stream.Type = "OnGuestLogout"
	Self.BroadcastStream(Stream)
}

func (Self *TSocket) Send(Message []byte, Connection gnet.Conn) {
	if Connection == nil {
		return
	}
	// log.Printf("TCP发出 %s\n", Message)
	Message = append(Message, '\n')
	Error := Connection.AsyncWrite(Message, func(Connect gnet.Conn, Error error) error {
		if Error != nil {
			log.Printf("发送失败: %v\n", Error)
		}
		return nil
	})
	if Error != nil {
		log.Printf("发送失败: %v\n", Error)
		return
	}
}

func (Self *TSocket) SendStream(Stream NStream, Connection gnet.Conn) {
	Message, _ := json.Marshal(Stream)
	Self.Send(Message, Connection)
}

func (Self *TSocket) BroadcastOther(Message []byte, SelfKey interface{}) {
	Pool.Range(func(Key, Value interface{}) bool {
		if SelfKey == Key {
			return true
		}
		Connection := Value.(*NClient).TCP.Connection
		Self.Send(Message, Connection)
		return true
	})
}

func (Self *TSocket) BroadcastStreamOther(Stream NStream) {
	Message, _ := json.Marshal(Stream)
	Self.BroadcastOther(Message, Stream.UserID)
}

func (Self *TSocket) Broadcast(Message []byte) {
	Self.BroadcastOther(Message, nil)
}

func (Self *TSocket) BroadcastStream(Stream NStream) {
	Message, _ := json.Marshal(Stream)
	Self.Broadcast(Message)
}

func StartTCP(Port int) {
	Instance := new(TSocket)
	go func() {
		go Instance.Cleanup()

		Url := fmt.Sprintf("tcp://0.0.0.0:%d", Port)
		Error := gnet.Run(
			Instance, Url,
			gnet.WithMulticore(true),
		)
		if Error != nil {
			log.Printf("存在错误: %v\n", Error)
			return
		}
	}()
	go func() {
		Ticker := time.NewTicker(CleanupInterval)
		for {
			<-Ticker.C
			Stream := NewNStream()
			Stream.UserID = -1
			Stream.Type = "Ping"
			Instance.BroadcastStream(Stream)
		}
	}()
}

func (Self *TSocket) HandleStream(Connect gnet.Conn, Stream NStream) {
	if Stream.Type == "Pong" {
		return
	}
	if Stream.Type == "OnGuestLogin" {
		// 验证用户登录
		if Password, OK := Stream.Payload["Password"].(string); OK {
			// 将UserID转换为字符串作为学号
			studentID := fmt.Sprintf("%d", Stream.UserID)
			user, err := ValidateUser(studentID, Password)

			response := NewNStream()
			response.Type = "OnLoginResponse"

			if err != nil {
				log.Printf("用户 %d 登录失败: %v\n", Stream.UserID, err)
				response.Payload["Success"] = false
				response.Payload["Message"] = err.Error()
				response.Payload["UserID"] = Stream.UserID
			} else {
				log.Printf("用户 %d 登录成功\n", Stream.UserID)
				response.Payload["Success"] = true
				response.Payload["Message"] = "登录成功"
				response.Payload["UserID"] = user.UserID

				// 登录成功后处理位置信息
				if SpawnAt, OK := Stream.Payload["SpawnAt"].(string); OK {
					if Client, OK := Pool.Load(Stream.UserID); OK {
						Client.(*NClient).UDP.Motion = SpawnAt
						Pool.Range(func(Key, Value interface{}) bool {
							if Stream.UserID == Key {
								return true
							}
							Client := Value.(*NClient)
							Login := NewNStream()
							Login.UserID = Key.(int)
							Login.Type = "OnGuestLogin"
							Login.Payload["SpawnAt"] = Client.UDP.Motion
							Self.SendStream(Login, Connect)
							return true
						})
					}
				}
			}

			Self.SendStream(response, Connect)
			return
		}
	}
	if Stream.Type == "OnUserRegister" {
		// 处理用户注册
		if StudentID, OK := Stream.Payload["StudentID"].(string); OK {
			if Password, OK := Stream.Payload["Password"].(string); OK {
				user, err := RegisterUser(StudentID, Password)

				response := NewNStream()
				response.Type = "OnRegisterResponse"

				if err != nil {
					log.Printf("用户注册失败 学号:%s 错误:%v\n", StudentID, err)
					response.Payload["Success"] = false
					response.Payload["Message"] = err.Error()
				} else {
					log.Printf("用户注册成功 学号:%s UserID:%d\n", StudentID, user.UserID)
					response.Payload["Success"] = true
					response.Payload["Message"] = "注册成功"
					response.Payload["UserID"] = user.UserID
				}

				Self.SendStream(response, Connect)
				return
			}
		}
	}
	if Stream.Type == "OnGuestLogout" {
		Self.CleanUser(Stream.UserID)
		log.Printf("用户 %d TCP连接退出\n", Stream.UserID)
		return
	}
	if Stream.Type == "CheckDailyLetterLimit" {
		if _, exists := Pool.Load(Stream.UserID); exists {
			LimitReached, err := CheckDailyLetterLimit(int(Stream.UserID), 3)
			if err != nil {
				log.Printf("信件限制检查失败: %v", err)
				return
			}
			response := NewNStream()
			response.Type = "OnCheckDailyLetterLimit"
			response.Payload["LimitReached"] = LimitReached
			Self.SendStream(response, Connect)
		}
		return
	}
	if Stream.Type == "LetterData" {
		log.Printf("收到信件\n")
		if _, exists := Pool.Load(Stream.UserID); exists {
			if Content, OK := Stream.Payload["Content"].(string); OK {
				if StyleIndexFloat, OK := Stream.Payload["StyleIndex"].(float64); OK {
					if Location, OK := Stream.Payload["Location"].(string); OK {
						if Rotation, OK := Stream.Payload["Rotation"].(string); OK {
							// 检查是否包含图片数据
							imageData, hasImage := Stream.Payload["ImageData"].(string)
							imageFormat, _ := Stream.Payload["ImageFormat"].(string)
							imageSizeFloat, _ := Stream.Payload["ImageSize"].(float64)
							imageSize := int64(imageSizeFloat)

							var letter Letter
							var err error

							if hasImage && imageData != "" {
								// 保存带图片的信件
								letter, err = SaveLetterWithImage(int(Stream.UserID), Content, int(StyleIndexFloat), Location, Rotation, imageData, imageFormat, imageSize)
								log.Printf("收到图片信件，格式: %s, 大小: %d bytes\n", imageFormat, imageSize)
							} else {
								// 保存普通信件
								letter, err = SaveLetter(int(Stream.UserID), Content, int(StyleIndexFloat), Location, Rotation)
							}

							if err != nil {
								log.Printf("保存信件失败: %v\n", err)
								// 发送错误响应给客户端
								errorResponse := NewNStream()
								errorResponse.Type = "OnLetterSaveError"
								errorResponse.Payload = map[string]interface{}{
									"Error": err.Error(),
								}
								Self.SendStream(errorResponse, Connect)
								return
							}

							log.Printf("信件内容: %s\n", Content)
							response := NewNStream()
							response.Type = "OnLetterDataReceived"
							response.Payload = map[string]interface{}{
								"Letters": []Letter{letter},
							}
							Self.BroadcastStream(response)
							return
						}
					}
				}
			}
		}
	}
	if Stream.Type == "RequestLetterObjects" {
		log.Printf("收到请求信件列表\n")
		letters, err := GetLatestLetters(10)
		if err != nil {
			log.Printf("获取信件列表失败: %v\n", err)
			return
		}

		// 将所有信件打包成一条消息发送
		response := NewNStream()
		response.Type = "OnLetterDataReceived"
		response.Payload = map[string]interface{}{
			"Letters": letters,
		}
		Self.SendStream(response, Connect)
		log.Printf("已发送 %d 封信件\n", len(letters))
		return
	}
	Self.BroadcastStreamOther(Stream)
}
