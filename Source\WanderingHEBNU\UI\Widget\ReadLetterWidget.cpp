#include "ReadLetterWidget.h"

#include "Components/MultiLineEditableText.h"
#include "Components/Image.h"
#include "Engine/Texture2D.h"
#include "WanderingHEBNU/Common/ImageUtils.h"
#include "WanderingHEBNU/Common/ImageCacheManager.h"

TObjectPtr<UButton> UReadLetterWidget::GetCloseButton() const
{
	return CloseButton;
}

void UReadLetterWidget::SetLetterContent(const FString& Content) const
{
	LetterContent -> SetText(FText::FromString(Content));
}

void UReadLetterWidget::SetLetterImage(const FString& ImageData, const FString& ImageFormat)
{
	if (ImageData.IsEmpty() || ImageFormat.IsEmpty())
	{
		ClearLetterImage();
		return;
	}

	// 使用缓存管理器获取或创建纹理
	UImageCacheManager* CacheManager = UImageCacheManager::GetInstance();
	CurrentImageTexture = CacheManager->GetOrCreateTexture(ImageData, ImageFormat);

	if (CurrentImageTexture && LetterImage)
	{
		// 设置图片纹理
		LetterImage->SetBrushFromTexture(CurrentImageTexture);
		LetterImage->SetVisibility(ESlateVisibility::Visible);

		UE_LOG(LogTemp, Log, TEXT("成功显示信件图片，格式: %s"), *ImageFormat);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("无法创建图片纹理"));
		ClearLetterImage();
	}
}

void UReadLetterWidget::ClearLetterImage()
{
	CurrentImageTexture = nullptr;

	if (LetterImage)
	{
		LetterImage->SetVisibility(ESlateVisibility::Hidden);
	}
}

bool UReadLetterWidget::HasLetterImage() const
{
	return CurrentImageTexture != nullptr;
}
