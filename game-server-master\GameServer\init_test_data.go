// +build ignore

package main

import (
	"fmt"
	"log"
	"GameServer/Core"
)

func main() {
	// 初始化数据库
	err := Core.InitDatabase()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 添加测试学生到白名单
	testStudents := []struct {
		StudentID string
		Name      string
	}{
		{"20210001", "张三"},
		{"20210002", "李四"},
		{"20210003", "王五"},
		{"20210004", "赵六"},
		{"20210005", "钱七"},
	}

	fmt.Println("开始添加测试学生到白名单...")
	for _, student := range testStudents {
		err := Core.AddStudentToWhitelist(student.StudentID, student.Name)
		if err != nil {
			fmt.Printf("添加学生 %s (%s) 失败: %v\n", student.StudentID, student.Name, err)
		} else {
			fmt.Printf("成功添加学生 %s (%s) 到白名单\n", student.StudentID, student.Name)
		}
	}

	fmt.Println("测试数据初始化完成！")
	fmt.Println("可以使用以下学号进行注册测试：")
	for _, student := range testStudents {
		fmt.Printf("- %s (%s)\n", student.StudentID, student.Name)
	}
}
