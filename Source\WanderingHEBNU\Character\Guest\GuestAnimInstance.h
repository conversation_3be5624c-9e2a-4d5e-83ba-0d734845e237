#pragma once

#include "CoreMinimal.h"
#include "../../Base/BaseAnimInstance.h"
#include "GuestAnimInstance.generated.h"

UCLASS()
class UGuestAnimInstance : public UBaseAnimInstance
{
	GENERATED_BODY()

public:
	virtual void NativeUpdateAnimation(float DeltaSeconds) override;

	void SetIsInAir(const bool bValue) { bIsInAir = bValue; }

	void SetIsWaving(const bool Value) { bIsWaving = Value; }

	bool IsWaving() const { return bIsWaving; }

	void SetSpeed(const float Value) { Speed = Value; }

	UFUNCTION(BlueprintCallable, Category = "挥手")
	void PlayWaveHandAnimation();

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "状态机变量", DisplayName = "是否在空中")
	bool bIsInAir = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "状态机变量", DisplayName = "方向")
	float Direction = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "状态机变量", DisplayName = "速度")
	float Speed = 0;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "是否在挥手")
	bool bIsWaving = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "挥手动画")
	UAnimMontage* WaveHandMontage;
};
