#include "BaseCharacter.h"

#include "Components/AudioComponent.h"
#include "../GamePlay/MainGameInstance.h"
#include "BaseAnimInstance.h"

ABaseCharacter::ABaseCharacter()
{
	PrimaryActorTick . bCanEverTick = true;
	AudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("AudioComponent"));
	AudioComponent -> SetupAttachment(GetMesh());
}

void ABaseCharacter::BeginPlay()
{
	Super::BeginPlay();
	MainGameInstance = Cast<UMainGameInstance>(GetGameInstance());
}

void ABaseCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

UBaseAnimInstance* ABaseCharacter::GetAnimInstance()
{
	if (AnimInstance) { return AnimInstance; }
	AnimInstance = Cast<UBaseAnimInstance>(GetMesh() -> GetAnimInstance());
	return AnimInstance;
}
