using UnrealBuildTool;

public class WanderingHEBNU : ModuleRules
{
	public WanderingHEBNU(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[]
		{
			"Core", "CoreUObject", "Engine", "InputCore", "EnhancedInput",
			"Sockets", "Networking", "Json", "JsonUtilities", "UMG", "Slate",
			"SlateCore", "Niagara", "AIModule", "NavigationSystem"
		});

		PrivateDependencyModuleNames.AddRange(new string[] { });
	}
}