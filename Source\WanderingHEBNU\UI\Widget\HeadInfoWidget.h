#pragma once

#include "CoreMinimal.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "HeadInfoWidget.generated.h"

UCLASS()
class UHeadInfoWidget : public UBaseUserWidget
{
	GENERATED_BODY()

public:
	void SetInfo(const FString& Info) const;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "信息显示", meta = (BindWidget))
	TObjectPtr<UTextBlock> InfoText;
};
