#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "UObject/Object.h"
#include "Log.generated.h"

UCLASS()
class ULog : public UObject
{
	GENERATED_BODY()

public:
	static void Info(const float Value, const bool bNotDisplay = false);

	static void Info(const FString& Value, const bool bNotDisplay = false);

	static void Info(const char* Value, const bool bNotDisplay = false);

	static FString TimeNow();
};
