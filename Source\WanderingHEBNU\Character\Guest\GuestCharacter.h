#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "../../GamePlay/Net/Payload.h"
#include "../../Base/BaseCharacter.h"
#include "GuestAnimInstance.h"
#include "../../Common/Log.h"
#include "GuestCharacter.generated.h"

UCLASS()
class AGuestCharacter : public ABaseCharacter
{
	GENERATED_BODY()

public:
	AGuestCharacter();

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

	void SetTargetMotion(const NetPacket::FGuestMotion& Value) { TargetMotion = Value; }

	void SetUserID(const int Value);

	int GetUserID() const { return UserID; }

	virtual UGuestAnimInstance* GetAnimInstance() override;

protected:
	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "用户ID")
	int UserID = -1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "移动容忍误差")
	float NetMotionRepTolerance = 0.1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "移动容忍误差(动画同步末段柔和处理用)")
	float NetMotionRepAnimTolerance = 5;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "移动线性插值平滑")
	float NetMotionRepSmooth = 5;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "移动速度")
	float NetMotionSpeed = 500;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "网络设置", DisplayName = "移速制动(速度除数)")
	float NetMotionSpeedBrake = 10;

	NetPacket::FGuestMotion TargetMotion;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "玩家头顶UI组件")
	TObjectPtr<UWidgetComponent> HeadWidgetComponent;

	void NetMotionReplicate(float DeltaTime);
};
