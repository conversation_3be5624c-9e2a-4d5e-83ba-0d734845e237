#include "LoginWidget.h"

TObjectPtr<UButton> ULoginWidget::GetLoginButton() const
{
	return LoginButton;
}

TObjectPtr<UButton> ULoginWidget::GetExitButton() const
{
	return ExitButton;
}

int ULoginWidget::GetAccountData() const
{
	//return AccountEditableTextBox -> GetText().ToString();
	FString AccountString = AccountEditableTextBox->GetText().ToString();
	return FCString::Atoi(*AccountString);
}

FString ULoginWidget::GetPasswordData() const
{
	return PasswordEditableTextBox -> GetText().ToString();
}

void ULoginWidget::OnLoginClicked()
{

}
