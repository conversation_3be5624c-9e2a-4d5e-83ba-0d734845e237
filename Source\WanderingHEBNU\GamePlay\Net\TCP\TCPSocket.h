#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "../../../GamePlay/Net/Payload.h"
#include "TCPSocket.generated.h"

class FTcpListener;

USTRUCT(BlueprintType)
struct FTCPLink
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "收发缓存大小")
	int BufferSize = 2 * 1024 * 1024;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "服务器端口")
	int ServerPort = 10001;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "帧间隔")
	float TickGap = 0.008;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "服务器IP")
	FString ServerIP = "127.0.0.1";
};

UCLASS()
class UTCPSocket : public UObject
{
	GENERATED_BODY()

public:
	void Start(const FTCPLink& Link);

	void Close();

	bool SendStream(NetPacket::FStream Stream) const;

	bool Send(const FString& Message) const;

	void OnConnected(TWeakObjectPtr<UTCPSocket> Self);

	void OnDisconnected(TWeakObjectPtr<UTCPSocket> Self);

	void OnMessageReceived(TWeakObjectPtr<UTCPSocket> Self);

	TFunction<void(FString BoundIP, int BoundPort)>& GetOnSocketStart() { return OnSocketStart; }

	TFunction<void()>& GetOnSocketClose() { return OnSocketClose; }

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)>& GetOnReceivedMessage() { return OnReceivedMessage; }

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)>& GetOnReceivedMessageInGameThread() { return OnReceivedMessageInGameThread; }

protected:
	TFunction<void(FString BoundIP, int BoundPort)> OnSocketStart;

	TFunction<void()> OnSocketClose;

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)> OnReceivedMessage;

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)> OnReceivedMessageInGameThread;

	FTCPLink Setting;

	TSharedPtr<class FTCPWorker> Worker;
};

class FTCPWorker : public FRunnable, public TSharedFromThis<FTCPWorker>
{
public:
	FTCPWorker(const FTCPLink& Link, TWeakObjectPtr<UTCPSocket> InOwner);

	virtual ~FTCPWorker() override;

	void Start();

	void AddMessage(const TArray<uint8>& Message) { OutBox . Enqueue(Message); }

	TArray<uint8> ReadMessage();

	virtual bool Init() override;

	virtual uint32 Run() override;

	virtual void Stop() override { bRun = false; }

	virtual void Exit() override { return; }

	void SocketShutdown();

	bool IsConnected() { return bConnected; }

protected:
	FThreadSafeBool bConnected = false;

	FThreadSafeBool bRun = false;

	FTCPLink Setting;

	TQueue<TArray<uint8>> InBox;

	TQueue<TArray<uint8>> OutBox;

	TWeakObjectPtr<UTCPSocket> ThreadSpawner;

	FSocket* Socket = nullptr;

	FRunnableThread* Thread = nullptr;

	bool SendBytes(const TArray<uint8>& Bytes);
};
