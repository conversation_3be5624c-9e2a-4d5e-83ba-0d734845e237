#include "TCPSocketComponent.h"

#include "TCPSocket.h"

UTCPSocketComponent::UTCPSocketComponent()
{
	PrimaryComponentTick . bCanEverTick = true;
}

void UTCPSocketComponent::BeginPlay()
{
	Super::BeginPlay();
	Socket = NewObject<UTCPSocket>();
}

void UTCPSocketComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UTCPSocketComponent::Start() const
{
	if (!Socket) { return; }
	Close();
	BindDelegate();
	Socket -> Start(Setting);
}

void UTCPSocketComponent::Close() const
{
	if (!Socket) { return; }
	Socket -> Close();
}

bool UTCPSocketComponent::SendStream(const NetPacket::FStream& Stream) const
{
	if (!Socket) { return false; }
	return Socket -> SendStream(Stream);
}

bool UTCPSocketComponent::Send(const FString& Message) const
{
	if (!Socket) { return false; }
	return Socket -> Send(Message);
}

void UTCPSocketComponent::BindDelegate() const
{
	if (!Socket) { return; }
	Socket -> GetOnSocketStart() = [this](const FString& BoundIP, const int BoundPort)
	{
		OnStartDelegate . Broadcast(BoundIP, BoundPort);
	};
	Socket -> GetOnSocketClose() = [this]()
	{
		OnCloseDelegate . Broadcast();
	};
	Socket -> GetOnReceivedMessage() = [this](const FString& Message, const FString& Endpoint, const int Port)
	{
		OnReceivedMessageDelegate . Broadcast(Message, Endpoint, Port);
	};
	Socket -> GetOnReceivedMessageInGameThread() = [this](const FString& Message, const FString& Endpoint, const int Port)
	{
		OnReceivedMessageInGameThreadDelegate . Broadcast(Message, Endpoint, Port);
	};
}
