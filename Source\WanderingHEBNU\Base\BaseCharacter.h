#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "BaseCharacter.generated.h"

class UBaseAnimInstance;
class UMainGameInstance;

UCLASS()
class ABaseCharacter : public ACharacter
{
	GENERATED_BODY()

public:
	ABaseCharacter();

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

	virtual UBaseAnimInstance* GetAnimInstance();

	TObjectPtr<UMainGameInstance> GetMainGameInstance() { return MainGameInstance; }

protected:
	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "游戏实例")
	TObjectPtr<UMainGameInstance> MainGameInstance = nullptr;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "动画实例")
	UBaseAnimInstance* AnimInstance = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "基础设置", DisplayName = "音频组件")
	TObjectPtr<UAudioComponent> AudioComponent = nullptr;
};
