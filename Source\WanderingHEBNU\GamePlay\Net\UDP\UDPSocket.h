#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "../../../GamePlay/Net/Payload.h"
#include "UDPSocket.generated.h"

class FUdpSocketReceiver;

USTRUCT(BlueprintType)
struct FUDPLink
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "收发缓存大小")
	int BufferSize = 2 * 1024 * 1024;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "服务器端口")
	int ServerPort = 10000;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "服务器IP")
	FString ServerIP = "127.0.0.1";
};

UCLASS()
class UUDPSocket : public UObject
{
	GENERATED_BODY()

public:
	bool Start(const FUDPLink& Link);

	bool Close();

	bool SendStream(NetPacket::FStream Stream) const;

	bool Send(const FString& Message) const;

	TFunction<void(FString BoundIP, int BoundPort)>& GetOnSocketStart() { return OnSocketStart; }

	TFunction<void()>& GetOnSocketClose() { return OnSocketClose; }

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)>& GetOnReceivedMessage() { return OnReceivedMessage; }

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)>& GetOnReceivedMessageInGameThread() { return OnReceivedMessageInGameThread; }

protected:
	TFunction<void(FString BoundIP, int BoundPort)> OnSocketStart;

	TFunction<void()> OnSocketClose;

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)> OnReceivedMessage;

	TFunction<void(const FString& Message, const FString& Endpoint, const int Port)> OnReceivedMessageInGameThread;

	FSocket* Socket;

	FUdpSocketReceiver* Receiver;

	TSharedPtr<FInternetAddr> RemoteAddress;

	ISocketSubsystem* SocketSubsystem;

	bool SendBytes(const TArray<uint8>& Bytes) const;

	bool PrintError() const;
};
