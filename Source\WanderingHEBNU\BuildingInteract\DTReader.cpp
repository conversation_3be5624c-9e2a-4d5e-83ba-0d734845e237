// Fill out your copyright notice in the Description page of Project Settings.


#include "DTReader.h"

// Sets default values
ADTReader::ADTReader()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void ADTReader::BeginPlay()
{
	Super::BeginPlay();
	BuildingInfoTable = Cast<UDataTable>(StaticLoadObject(UDataTable::StaticClass(),nullptr,TEXT("/Script/Engine.DataTable'/Game/Blueprint/BuildingInteract/DT_BudingInfo.DT_BudingInfo'")));

}

// Called every frame
void ADTReader::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

FString ADTReader::FindInfo(FString RowName)
{
	if (BuildingInfoTable)
	{
		FName RowFName = FName(*RowName);
		FBuildingInfo* BuildingInfo = BuildingInfoTable->FindRow<FBuildingInfo>(RowFName, TEXT(""));
		if (BuildingInfo)
		{
			return FString::Printf(TEXT("建筑名: %s\n简介: %s"), *BuildingInfo->Name, *BuildingInfo->Description);
		}
	}
	return FString("Row not found");
}
