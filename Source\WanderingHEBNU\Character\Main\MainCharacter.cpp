#include "MainCharacter.h"

#include "Blueprint/UserWidget.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "../../GamePlay/MainGameInstance.h"
#include "../../GamePlay/Net/NetManager.h"
#include "../../UI/GameHUD.h"

AMainCharacter::AMainCharacter()
{
	PrimaryActorTick . bCanEverTick = true;

	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;

	GetCharacterMovement() -> bOrientRotationToMovement = true;
	GetCharacterMovement() -> RotationRate = FRotator(0, 600, 0);
	GetCharacterMovement() -> JumpZVelocity = 700;
	GetCharacterMovement() -> AirControl = 0.35;
	GetCharacterMovement() -> MaxWalkSpeed = 600;
	GetCharacterMovement() -> MinAnalogWalkSpeed = 20;
	GetCharacterMovement() -> BrakingDecelerationWalking = 2000;
	GetCharacterMovement() -> BrakingDecelerationFalling = 1500;

	SpringArmComponent = CreateDefaultSubobject<USpringArmComponent>(TEXT("SpringArmComponent"));
	SpringArmComponent -> TargetArmLength = 400;
	SpringArmComponent -> bUsePawnControlRotation = true;
	SpringArmComponent -> bDoCollisionTest = false;
	SpringArmComponent -> SetupAttachment(GetRootComponent());

	CameraComponent = CreateDefaultSubobject<UCameraComponent>(TEXT("CameraComponent"));
	CameraComponent -> bUsePawnControlRotation = false;
	CameraComponent -> SetupAttachment(SpringArmComponent);
}

void AMainCharacter::BeginPlay()
{
	Super::BeginPlay();
}

void AMainCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	NetMotionReplicate(DeltaTime);
}

UMainAnimInstance* AMainCharacter::GetAnimInstance()
{
	if (AnimInstance) { return Cast<UMainAnimInstance>(AnimInstance); }
	AnimInstance = Cast<UMainAnimInstance>(GetMesh() -> GetAnimInstance());
	return Cast<UMainAnimInstance>(AnimInstance);
}

void AMainCharacter::NetMotionReplicate(const float DeltaTime)
{
	if (!NetManager || !IsValid(NetManager)) { return; }
	NetManager -> SendMotion();
}

void AMainCharacter::LoginSuccess()
{
	NetManager = MainGameInstance -> GetNetManager();
	if (GameHUDSubclassOf)
	{
		GameHUD = CreateWidget<UGameHUD>(Cast<APlayerController>(GetController()), GameHUDSubclassOf);
		GameHUD -> Open();
		if (NetManager)
		{
			// FTimerHandle TimerHandle;
			// GetWorld() -> GetTimerManager() . SetTimer(TimerHandle, [this]()
			// {
			// 	ULog::Info(TEXT("Test : 请求信件！"));
			// 	NetManager -> RequestLetterObjects();
			// }, 1, false);
			ULog::Info(TEXT("Test : 请求信件！"));
			NetManager -> RequestLetterObjects();
			const FString UserInfo = TEXT("UID: ") + FString::FromInt(NetManager -> GetUserID());
			GameHUD -> SetUserInfo(UserInfo);
		}
	}
}
