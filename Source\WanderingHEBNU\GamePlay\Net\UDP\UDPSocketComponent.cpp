#include "UDPSocketComponent.h"

UUDPSocketComponent::UUDPSocketComponent()
{
	PrimaryComponentTick . bCanEverTick = true;
}

void UUDPSocketComponent::BeginPlay()
{
	Super::BeginPlay();
	Socket = NewObject<UUDPSocket>();
}

void UUDPSocketComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UUDPSocketComponent::Start() const
{
	if (!Socket) { return; }
	Close();
	BindDelegate();
	Socket -> Start(Setting);
}

void UUDPSocketComponent::Close() const
{
	if (!Socket) { return; }
	Socket -> Close();
}

bool UUDPSocketComponent::SendStream(const NetPacket::FStream& Stream) const
{
	if (!Socket) { return false; }
	return Socket -> SendStream(Stream);
}

bool UUDPSocketComponent::Send(const FString& Message) const
{
	if (!Socket) { return false; }
	return Socket -> Send(Message);
}

void UUDPSocketComponent::BindDelegate() const
{
	if (!Socket) { return; }
	Socket -> GetOnSocketStart() = [this](const FString& BoundIP, const int BoundPort)
	{
		OnStartDelegate . Broadcast(BoundIP, BoundPort);
	};
	Socket -> GetOnSocketClose() = [this]()
	{
		OnCloseDelegate . Broadcast();
	};
	Socket -> GetOnReceivedMessage() = [this](const FString& Message, const FString& Endpoint, const int Port)
	{
		OnReceivedMessageDelegate . Broadcast(Message, Endpoint, Port);
	};
	Socket -> GetOnReceivedMessageInGameThread() = [this](const FString& Message, const FString& Endpoint, const int Port)
	{
		OnReceivedMessageInGameThreadDelegate . Broadcast(Message, Endpoint, Port);
	};
}
