#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Common.generated.h"

UCLASS()
class UCommon : public UObject
{
	GENERATED_BODY()

public:
	static FString Salt(int Bit = 10);

	/**
	 * 碰撞查询类型
	 */
	static TArray<TEnumAsByte<EObjectTypeQuery>> GetCommonQueryType()
	{
		TArray<TEnumAsByte<EObjectTypeQuery>> Type;
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Pawn));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Visibility));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_WorldStatic));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_WorldDynamic));
		return Type;
	}

	static TArray<TEnumAsByte<EObjectTypeQuery>> GetVisibilityQueryType()
	{
		TArray<TEnumAsByte<EObjectTypeQuery>> Type;
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Visibility));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_WorldStatic));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_WorldDynamic));
		return Type;
	}

	static TArray<TEnumAsByte<EObjectTypeQuery>> GetAllQueryType()
	{
		TArray<TEnumAsByte<EObjectTypeQuery>> Type;
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_WorldStatic));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_WorldDynamic));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Pawn));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Visibility));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Camera));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_PhysicsBody));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Vehicle));
		Type . Emplace(UEngineTypes::ConvertToObjectType(ECC_Destructible));
		return Type;
	}
};
