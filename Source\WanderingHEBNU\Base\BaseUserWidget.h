#pragma once

#include "CoreMinimal.h"
#include "Components/Image.h"
#include "Components/Button.h"
#include "Components/Border.h"
#include "Components/Slider.h"
#include "Components/TileView.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "Blueprint/UserWidget.h"
#include "Components/MenuAnchor.h"
#include "Components/ComboBoxKey.h"
#include "Components/ProgressBar.h"
#include "Components/RichTextBlock.h"
#include "Components/WidgetSwitcher.h"
#include "Components/ComboBoxString.h"
#include "Animation/WidgetAnimation.h"
#include "Components/CircularThrobber.h"
#include "../GamePlay/MainGameInstance.h"
#include "../Common/Log.h"
#include "BaseUserWidget.generated.h"

UCLASS()
class UBaseUserWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	virtual void Open();

	virtual void Close();

	UFUNCTION(BlueprintCallable)
	void SetInputUI();

	UFUNCTION(BlueprintCallable)
	void SetInputGame();

protected:
	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "游戏实例")
	TObjectPtr<UMainGameInstance> MainGameInstance;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "玩家控制器")
	TObjectPtr<APlayerController> Controller;

	TObjectPtr<UMainGameInstance> GetMainGameInstance()
	{
		if (!MainGameInstance || !IsValid(MainGameInstance))
		{
			MainGameInstance = Cast<UMainGameInstance>(GetGameInstance());
		}
		return MainGameInstance;
	}

	TObjectPtr<APlayerController> GetController()
	{
		if (!Controller || !IsValid(Controller))
		{
			Controller = GetWorld() -> GetFirstPlayerController();
		}
		return Controller;
	}
};
