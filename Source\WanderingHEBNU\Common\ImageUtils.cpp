#include "ImageUtils.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Base64.h"
#include "Engine/Texture2D.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Modules/ModuleManager.h"
#include "RenderUtils.h"
#include "TextureResource.h"
#include "HAL/UnrealMemory.h"

// 定义锁定常量（如果UE5.4中不存在）
#ifndef LOCK_READ_WRITE
#define LOCK_READ_WRITE 3
#endif

// 支持的图片格式定义
const TArray<FString> UImageUtils::SupportedFormats = {
	TEXT("JPG"),
	TEXT("JPEG"),
	TEXT("PNG"),
	TEXT("BMP")
};

bool UImageUtils::ImageFileToBase64(const FString& FilePath, FString& OutBase64, FString& OutFormat, int64& OutSize)
{
	// 检查文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("图片文件不存在: %s"), *FilePath);
		return false;
	}

	// 获取文件格式
	OutFormat = GetImageFormatFromPath(FilePath).ToUpper();
	if (!IsValidImageFormat(OutFormat))
	{
		UE_LOG(LogTemp, Warning, TEXT("不支持的图片格式: %s"), *OutFormat);
		return false;
	}

	// 读取文件数据
	TArray<uint8> FileData;
	if (!FFileHelper::LoadFileToArray(FileData, *FilePath))
	{
		UE_LOG(LogTemp, Warning, TEXT("无法读取图片文件: %s"), *FilePath);
		return false;
	}

	OutSize = FileData.Num();

	// 检查文件大小
	if (!IsValidImageSize(OutSize))
	{
		UE_LOG(LogTemp, Warning, TEXT("图片文件过大: %lld bytes"), OutSize);
		return false;
	}

	// 转换为Base64
	OutBase64 = FBase64::Encode(FileData);

	UE_LOG(LogTemp, Log, TEXT("图片转换成功: %s, 格式: %s, 大小: %lld bytes"), *FilePath, *OutFormat, OutSize);
	return true;
}

UTexture2D* UImageUtils::Base64ToTexture2D(const FString& Base64String, const FString& Format)
{
	if (Base64String.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("Base64字符串为空"));
		return nullptr;
	}

	// 解码Base64
	TArray<uint8> ImageData;
	if (!FBase64::Decode(Base64String, ImageData))
	{
		UE_LOG(LogTemp, Warning, TEXT("Base64解码失败"));
		return nullptr;
	}

	// 获取图片包装器模块
	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>(FName("ImageWrapper"));

	// 确定图片格式
	EImageFormat ImageFormat = EImageFormat::Invalid;
	if (Format == TEXT("JPG") || Format == TEXT("JPEG"))
	{
		ImageFormat = EImageFormat::JPEG;
	}
	else if (Format == TEXT("PNG"))
	{
		ImageFormat = EImageFormat::PNG;
	}
	else if (Format == TEXT("BMP"))
	{
		ImageFormat = EImageFormat::BMP;
	}

	if (ImageFormat == EImageFormat::Invalid)
	{
		UE_LOG(LogTemp, Warning, TEXT("不支持的图片格式: %s"), *Format);
		return nullptr;
	}

	// 创建图片包装器
	TSharedPtr<IImageWrapper> ImageWrapper = ImageWrapperModule.CreateImageWrapper(ImageFormat);
	if (!ImageWrapper.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("无法创建图片包装器"));
		return nullptr;
	}

	// 设置压缩数据
	if (!ImageWrapper->SetCompressed(ImageData.GetData(), ImageData.Num()))
	{
		UE_LOG(LogTemp, Warning, TEXT("无法设置压缩数据"));
		return nullptr;
	}

	// 获取原始数据
	TArray<uint8> UncompressedBGRA;
	if (!ImageWrapper->GetRaw(ERGBFormat::BGRA, 8, UncompressedBGRA))
	{
		UE_LOG(LogTemp, Warning, TEXT("无法获取原始图片数据"));
		return nullptr;
	}

	// 创建纹理 - 使用UE5.4兼容的方法
	UTexture2D* Texture = UTexture2D::CreateTransient(ImageWrapper->GetWidth(), ImageWrapper->GetHeight(), PF_B8G8R8A8);
	if (!Texture)
	{
		UE_LOG(LogTemp, Warning, TEXT("无法创建纹理"));
		return nullptr;
	}

	// 获取纹理平台数据
	FTexturePlatformData* PlatformData = Texture->GetPlatformData();
	if (!PlatformData || PlatformData->Mips.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("无法获取纹理平台数据"));
		return nullptr;
	}

	// 复制数据到纹理 - 使用UE5.4兼容的API
	FTexture2DMipMap& Mip = PlatformData->Mips[0];

	// 尝试使用更兼容的锁定方式
	void* TextureData = Mip.BulkData.Lock(LOCK_READ_WRITE);
	if (TextureData)
	{
		FMemory::Memcpy(TextureData, UncompressedBGRA.GetData(), UncompressedBGRA.Num());
		Mip.BulkData.Unlock();

		// 更新纹理资源
		Texture->UpdateResource();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("无法锁定纹理数据"));
		return nullptr;
	}

	UE_LOG(LogTemp, Log, TEXT("成功创建纹理: %dx%d"), ImageWrapper->GetWidth(), ImageWrapper->GetHeight());
	return Texture;
}

bool UImageUtils::CompressImageData(const TArray<uint8>& OriginalData, int32 Quality, TArray<uint8>& OutCompressedData)
{
	// 这里可以实现图片压缩逻辑
	// 目前简单返回原始数据
	OutCompressedData = OriginalData;
	return true;
}

bool UImageUtils::IsValidImageFormat(const FString& Format)
{
	return SupportedFormats.Contains(Format.ToUpper());
}

bool UImageUtils::IsValidImageSize(int64 Size)
{
	return Size > 0 && Size <= MAX_IMAGE_SIZE;
}

FString UImageUtils::GetImageFormatFromPath(const FString& FilePath)
{
	FString Extension = FPaths::GetExtension(FilePath);
	return Extension.ToUpper();
}

TArray<FString> UImageUtils::GetSupportedImageFormats()
{
	return SupportedFormats;
}
