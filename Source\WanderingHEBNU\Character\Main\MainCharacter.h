#pragma once

#include "CoreMinimal.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "../../Base/BaseCharacter.h"
#include "MainAnimInstance.h"
#include "MainCharacter.generated.h"

class ANetManager;
class UGameHUD;

UCLASS()
class AMainCharacter : public ABaseCharacter
{
	GENERATED_BODY()

public:
	AMainCharacter();

	virtual void BeginPlay() override;

	virtual void Tick(float DeltaTime) override;

	virtual UMainAnimInstance* GetAnimInstance() override;

	void LoginSuccess();

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "玩家设置", DisplayName = "玩家HUD模板")
	TSubclassOf<UGameHUD> GameHUDSubclassOf;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "网络管理")
	TObjectPtr<ANetManager> NetManager;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "玩家HUD")
	TObjectPtr<UGameHUD> GameHUD;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "玩家设置", DisplayName = "摄像机摇臂")
	TObjectPtr<USpringArmComponent> SpringArmComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "玩家设置", DisplayName = "摄像机")
	TObjectPtr<UCameraComponent> CameraComponent;

	void NetMotionReplicate(float DeltaTime);
};
