#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "../../../Common/Log.h"
#include "UDPSocket.h"
#include "UDPSocketComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnUDPSocketStartDelegate, const FString&, BoundIP, int, BoundPort);

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnUDPSocketCloseDelegate);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnUDPSocketReceivedMessageDelegate, const FString&, Message, const FString&, Endpoint, const int, Port);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class UUDPSocketComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UUDPSocketComponent();

	virtual void BeginPlay() override;

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	void Start() const;

	void Close() const;

	bool SendStream(const NetPacket::FStream& Stream) const;

	UFUNCTION(BlueprintCallable)
	bool Send(const FString& Message) const;

	FOnUDPSocketStartDelegate& GetOnStartDelegate() { return OnStartDelegate; }

	FOnUDPSocketCloseDelegate& GetOnCloseDelegate() { return OnCloseDelegate; }

	FOnUDPSocketReceivedMessageDelegate& GetOnReceivedMessageDelegate() { return OnReceivedMessageDelegate; }

	FOnUDPSocketReceivedMessageDelegate& GetOnReceivedMessageInGameThreadDelegate() { return OnReceivedMessageInGameThreadDelegate; }

protected:
	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "开始回调代理")
	FOnUDPSocketStartDelegate OnStartDelegate;

	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "关闭回调代理")
	FOnUDPSocketCloseDelegate OnCloseDelegate;

	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "接收消息回调代理（UDP线程）")
	FOnUDPSocketReceivedMessageDelegate OnReceivedMessageDelegate;

	UPROPERTY(BlueprintAssignable, Category = "事件", DisplayName = "接收消息回调代理（Game线程）")
	FOnUDPSocketReceivedMessageDelegate OnReceivedMessageInGameThreadDelegate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "连接设置", DisplayName = "连接设置")
	FUDPLink Setting;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "套接字")
	TObjectPtr<UUDPSocket> Socket;

	void BindDelegate() const;
};
