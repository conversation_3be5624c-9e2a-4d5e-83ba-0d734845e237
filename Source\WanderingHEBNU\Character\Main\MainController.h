#pragma once

#include "CoreMinimal.h"
#include "InputMappingContext.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "GameFramework/PlayerController.h"
#include "MainController.generated.h"

class UExitWidget;
class UHeadInfoWidget;
class UReadLetterWidget;
class USendLetterWidget;
class UBaseUserWidget;
class UWidgetComponent;
class UMainGameInstance;
class AMainCharacter;
class ALetterObject;
class UMainMenuWidget;
class ULoginWidget;
class URegisterWidget;

UCLASS()
class AMainController : public APlayerController
{
	GENERATED_BODY()

public:
	AMainController();

	virtual void BeginPlay() override;

	virtual void BeginDestroy() override;

	virtual void SetupInputComponent() override;

	virtual void Tick(float DeltaSeconds) override;

	void HandleOnCheckDailyLetterLimit(bool bLimitReached);

	int GetUsername() const{ return Username;};

	FString GetPassword() const{ return Password;};

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "信件模板类")
	TArray<TSubclassOf<ALetterObject>> LettersSubclassOf;

	UFUNCTION()
	void HandleExitCancel();

	UFUNCTION(BlueprintCallable, Category = "游戏操作")
	void ExitGame();

protected:
	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "控制的玩家类")
	TObjectPtr<AMainCharacter> MainCharacter;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "内部设置", DisplayName = "游戏实例")
	TObjectPtr<UMainGameInstance> MainGameInstance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "移动操作映射")
	TObjectPtr<UInputAction> MoveAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "视角操作映射")
	TObjectPtr<UInputAction> LookAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "跳跃操作映射")
	TObjectPtr<UInputAction> JumpAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "发送信件操作映射")
	TObjectPtr<UInputAction> SendLetterAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "读取信件操作映射")
	TObjectPtr<UInputAction> ReadLetterAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "挥手操作映射")
	TObjectPtr<UInputAction> WaveHandAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "行走冲刺切换映射")
	TObjectPtr<UInputAction> SprintAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "退出映射")
	TObjectPtr<UInputAction> ExitAction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "输入映射", DisplayName = "输入上下文")
	TObjectPtr<UInputMappingContext> InputMappingContext;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "发送信件UI组件")
	TSubclassOf<USendLetterWidget> SendLetterWidget;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "发送信件UI实例")
	TObjectPtr<USendLetterWidget> SendLetterWidgetInstance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "读取信件UI组件")
	TSubclassOf<UReadLetterWidget> ReadLetterWidget;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "菜单界面")
	TSubclassOf<UMainMenuWidget> MenuSubclassOf;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "菜单界面")
	TObjectPtr<UMainMenuWidget> MainMenuWidget;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "读取信件UI实例")
	TObjectPtr<UReadLetterWidget> ReadLetterWidgetInstance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "退出UI组件")
	TSubclassOf<UExitWidget> ExitWidget;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "退出UI实例")
	TObjectPtr<UExitWidget> ExitWidgetInstance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "登录界面")
	TSubclassOf<ULoginWidget> LoginSubclassOf;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "注册界面模板")
	TSubclassOf<URegisterWidget> RegisterSubclassOf;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "登录界面")
	TObjectPtr<ULoginWidget> LoginWidget;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "注册界面")
	TObjectPtr<URegisterWidget> RegisterWidget;

	UFUNCTION(BlueprintCallable, Category="信件操作")
	void OnGenerateLetter();

	UFUNCTION(BlueprintCallable, Category="信件操作")
	void OnCancel();

	UFUNCTION(BlueprintCallable, Category="登录操作")
	void OnLoginRequest();

	UFUNCTION(BlueprintCallable, Category="登录判断")
	void OnLoginJudge();

	UFUNCTION(BlueprintCallable, Category="退出登录界面")
	void OnExit();

	UFUNCTION(BlueprintCallable, Category="登录验证")
	void OnLoginVerify(bool bSuccess, const FString& Message);

	UFUNCTION(BlueprintCallable, Category="注册操作")
	void OnRegisterRequest();

	UFUNCTION(BlueprintCallable, Category="注册判断")
	void OnRegisterJudge();

	UFUNCTION(BlueprintCallable, Category="注册验证")
	void OnRegisterVerify(bool bSuccess, const FString& Message);

	UFUNCTION(BlueprintCallable, Category="返回主菜单")
	void OnBackToMainMenu();

	void Move(const FInputActionValue& Value);

	void Look(const FInputActionValue& Value);

	void Jump();

	void SendLetter();

	void CheckLetterLimit();

	void ReadLetter();

	void WaveHand();

	UFUNCTION(BlueprintCallable, Category="挥手蒙太奇结束操作")
	void OnWaveAnimationEnded(UAnimMontage* Montage, bool bInterrupted);

	bool bIsOpenTip = false;

	bool bIsRead = false;

	bool bIsSend = false;

	bool bIsSprinting;

	int Username;

	FString Password;

	void ToggleSprint();

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "提示UI组件")
	TSubclassOf<UHeadInfoWidget> HeadInfoWidget;

	UPROPERTY(VisibleInstanceOnly, BlueprintReadWrite, Category = "UI设置", DisplayName = "提示UI实例")
	TObjectPtr<UHeadInfoWidget> HeadInfoWidgetInstance;
};
