#include "MainMenuWidget.h"
#include "../../UI/Widget/LoginWidget.h"

void UMainMenuWidget::NativeConstruct()
{
	Super::NativeConstruct();
	if (ExitButton)
	{
		ExitButton->OnClicked.AddDynamic(this, &UMainMenuWidget::OnExitClicked);
	}
}

void UMainMenuWidget::Open()
{
	Super::Open();
	SetInputUI();
}

TObjectPtr<UButton> UMainMenuWidget::GetLoginButton() const
{
	return LoginButton;
}

TObjectPtr<UButton> UMainMenuWidget::GetExitButton() const
{
	return ExitButton;
}

TObjectPtr<UButton> UMainMenuWidget::GetRegisterButton() const
{
	return RegisterButton;
}

TObjectPtr<UButton> UMainMenuWidget::GetSettingButton() const
{
	return SettingButton;
}

void UMainMenuWidget::OnExitClicked()
{
	// RemoveFromParent();
	// SetInputGame();
}

