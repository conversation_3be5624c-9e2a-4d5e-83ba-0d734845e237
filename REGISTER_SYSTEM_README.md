# WanderingHEBNU 用户注册系统

## 功能概述

本系统实现了完整的用户注册功能，包括白名单验证机制、用户注册、登录验证等功能。

## 系统架构

### 数据库设计

1. **students表（白名单表）**
   - `student_id`: 学号（主键）
   - `name`: 学生姓名
   - `create_time`: 创建时间

2. **users表（用户表）**
   - `user_id`: 用户ID（主键，自增）
   - `student_id`: 学号（唯一索引）
   - `password`: 密码
   - `create_time`: 注册时间
   - `last_login_time`: 最后登录时间

### 网络协议

#### 注册协议
- **请求类型**: `OnUserRegister`
- **请求数据**:
  ```json
  {
    "UserID": -1,
    "Type": "OnUserRegister",
    "Payload": {
      "StudentID": "学号",
      "Password": "密码"
    }
  }
  ```

- **响应类型**: `OnRegisterResponse`
- **响应数据**:
  ```json
  {
    "Type": "OnRegisterResponse",
    "Payload": {
      "Success": true/false,
      "Message": "响应消息",
      "UserID": 用户ID（注册成功时）
    }
  }
  ```

#### 登录协议（已更新）
- **请求类型**: `OnGuestLogin`
- **响应类型**: `OnLoginResponse`
- 现在包含用户验证逻辑

## 客户端实现

### UI组件
1. **RegisterWidget**: 注册界面
   - 学号输入框
   - 密码输入框
   - 确认密码输入框
   - 注册按钮
   - 返回按钮

2. **MainMenuWidget**: 主菜单（已扩展）
   - 添加了注册按钮功能

### 网络管理
- **NetManager**: 扩展了注册相关的网络通信
  - `SendRegister()`: 发送注册请求
  - `OnRegisterResult`: 注册结果事件

### 控制器
- **MainController**: 扩展了注册流程控制
  - 注册界面管理
  - 输入验证
  - 错误处理

## 服务端实现

### 数据库操作
- `IsStudentInWhitelist()`: 检查学号是否在白名单中
- `IsUserExists()`: 检查用户是否已存在
- `RegisterUser()`: 注册新用户
- `ValidateUser()`: 验证用户登录
- `AddStudentToWhitelist()`: 添加学生到白名单

### 网络处理
- TCP协议处理注册和登录请求
- 完整的错误处理和响应机制

## 部署和测试

### 1. 初始化测试数据
```bash
cd game-server-master/GameServer
go run init_test_data.go
```

### 2. 启动服务器
```bash
cd game-server-master/GameServer
go run Main.go
```

### 3. 测试账号
系统预置了以下测试学号：
- 20210001 (张三)
- 20210002 (李四)
- 20210003 (王五)
- 20210004 (赵六)
- 20210005 (钱七)

### 4. 注册流程
1. 启动客户端
2. 在主菜单点击"注册"按钮
3. 输入白名单中的学号
4. 设置密码并确认
5. 点击注册
6. 注册成功后返回主菜单进行登录

### 5. 登录流程
1. 在主菜单点击"登录"按钮
2. 输入已注册的学号
3. 输入对应密码
4. 登录成功进入游戏

## 安全注意事项

1. **密码安全**: 当前实现中密码以明文存储，生产环境中应使用哈希加密
2. **输入验证**: 客户端和服务端都进行了基本的输入验证
3. **白名单机制**: 只有预先添加到白名单的学号才能注册

## 错误处理

### 注册错误
- 学号不在白名单中
- 用户已存在
- 输入验证失败
- 密码不一致

### 登录错误
- 用户名或密码错误
- 数据库连接失败

## 扩展功能

### 可能的改进
1. 密码强度验证
2. 邮箱验证
3. 找回密码功能
4. 管理员界面
5. 用户信息管理

## 技术栈

- **服务端**: Go + gnet + GORM + MySQL
- **客户端**: UE5 + C++
- **通信协议**: TCP + JSON
- **数据库**: MySQL

## 文件结构

### 服务端新增文件
- `Core/Database.go`: 扩展了用户和学生表的数据库操作
- `init_test_data.go`: 测试数据初始化脚本

### 客户端新增文件
- `UI/Widget/RegisterWidget.h/cpp`: 注册界面组件
- `GamePlay/Net/Payload.h`: 扩展了注册协议数据包

### 修改的文件
- `Core/TSocket.go`: 添加了注册和登录验证处理
- `GamePlay/Net/NetManager.h/cpp`: 扩展了注册网络功能
- `Character/Main/MainController.h/cpp`: 添加了注册流程控制
