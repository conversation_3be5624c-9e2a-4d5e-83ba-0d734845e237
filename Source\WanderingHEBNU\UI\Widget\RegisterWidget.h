#pragma once

#include "CoreMinimal.h"
#include "Components/EditableTextBox.h"
#include "WanderingHEBNU/Base/BaseUserWidget.h"
#include "RegisterWidget.generated.h"

UCLASS()
class URegisterWidget : public UBaseUserWidget
{
	GENERATED_BODY()
	
public:

	TObjectPtr<UButton> GetRegisterButton() const;
	
	TObjectPtr<UButton> GetBackButton() const;

	FString GetStudentIDData() const;

	FString GetPasswordData() const;

	FString GetConfirmPasswordData() const;
	
protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "注册按钮", meta = (BindWidget))
	TObjectPtr<UButton> RegisterButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "返回按钮", meta = (BindWidget))
	TObjectPtr<UButton> BackButton;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "学号文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> StudentIDEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "密码文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> PasswordEditableTextBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI设置", DisplayName = "确认密码文本框", meta = (BindWidget))
	TObjectPtr<UEditableTextBox> ConfirmPasswordEditableTextBox;

	UFUNCTION(BlueprintCallable, Category = "注册操作")
	void OnRegisterClicked();
	
};
