#include "MainGameMode.h"

#include "../GamePlay/MainGameInstance.h"
#include "../GamePlay/Net/NetManager.h"

AMainGameMode::AMainGameMode()
{
	PrimaryActorTick . bCanEverTick = true;
}

void AMainGameMode::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);
}

void AMainGameMode::BeginPlay()
{
	Super::BeginPlay();
	MainGameInstance = Cast<UMainGameInstance>(GetGameInstance());
	if (!MainGameInstance) { return; }
	if (NetManagerSubclassOf)
	{
		FActorSpawnParameters Parameter;
		Parameter . Owner = this;
		Parameter . bNoFail = true;
		Parameter . SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
		NetManager = GetWorld() -> SpawnActor<ANetManager>(NetManagerSubclassOf, Parameter);
		// TODO 后续需要换成真实的用户ID
		NetManager -> Start();
		// NetManager -> SendLogin();
		MainGameInstance -> SetNetManager(NetManager);
	}
}

void AMainGameMode::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	if (!MainGameInstance) { return; }
	Super::EndPlay(EndPlayReason);
}

void AMainGameMode::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
}
